import 'package:example/feature/home/<USER>/home_repository.dart';
import 'package:example/feature/home/<USER>/home_repository_impl.dart';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:get_it/get_it.dart';

class HomeModule {
  static void init() {
    GetIt.I.registerSingleton<HomeRepository>(
      HomeRepositoryImpl(client: GetIt.I<IFilestoreClient>()),
    );
  }

  static void updateRepository() {
    // Đảm bảo rằng HomeModule luôn cập nhật lại client khi token thay đổi
    GetIt.I.unregister<HomeRepository>(); // Hủy đăng ký HomeRepository cũ
    GetIt.I.registerSingleton<HomeRepository>(
      HomeRepositoryImpl(
          client:
              GetIt.I.get<IFilestoreClient>()), // Lấy lại FilestoreClient mới
    );
  }
}
