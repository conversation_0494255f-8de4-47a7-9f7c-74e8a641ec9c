import 'package:dio/dio.dart';
import 'package:example/feature/home/<USER>/home_repository.dart';
import 'package:example/feature/home/<USER>/upload_task.dart';
import 'package:filestore_sdk/filestore_sdk.dart';

import 'package:example/core/network/response/base_response.dart'
    as app_response;

class HomeRepositoryImpl extends HomeRepository {
  final IFilestoreClient client;

  HomeRepositoryImpl({required this.client});

  @override
  Future<void> uploadFile(
    UploadFile file, {
    void Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  }) async {
    await client.uploadFile(
      UpFile(
        path: file.path,
        name: file.name,
        size: file.size,
      ),
      onSuccess: success,
      onProgress: progress,
      onError: error,
      cancelToken: cancelToken,
    );
  }

  @override
  Future<void> resumeFile(
    UploadFile file, {
    void Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  }) async {
    print("resumeFile ${file.uploadState?.chunkIndex}");

    await client.resumedFile(
      UpFile(
          path: file.path,
          name: file.name,
          size: file.size,
          uploadState: file.uploadState),
      onSuccess: success,
      onProgress: progress,
      onError: error,
      cancelToken: cancelToken,
    );
  }

  @override
  Future<app_response.BaseResponse<List<String>>> uploadMultipleFiles(
      List<UploadFile> files,
      {void Function(double progres)? progress,
      CancelToken? cancelToken}) {
    // TODO: implement uploadMultipleFiles
    throw UnimplementedError();
  }
}
