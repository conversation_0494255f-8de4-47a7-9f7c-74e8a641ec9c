import 'package:example/feature/home/<USER>/widget/setup_token_button.dart';
import 'package:example/main_module.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:example/core/theme/app_color.dart';
import 'package:example/feature/home/<USER>/upload_task.dart';
import 'package:example/feature/home/<USER>/bloc/task/task_cubit.dart';
import 'package:example/feature/home/<USER>/widget/multiple_task_item.dart';
import 'package:example/feature/home/<USER>/widget/select_file_button.dart';
import 'package:example/feature/home/<USER>/widget/single_task_item.dart';
import 'package:image_picker/image_picker.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TaskCubit(),
      child: const HomeLayout(),
    );
  }
}

class HomeLayout extends StatefulWidget {
  const HomeLayout({super.key});

  @override
  State<HomeLayout> createState() => _HomeLayoutState();
}

class _HomeLayoutState extends State<HomeLayout>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  bool isSingleUpload = true;

  @override
  void initState() {
    _tabController = TabController(length: 2, vsync: this);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Text(
          'File Store Upload File',
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [Text("Single Upload"), Text("Setting")],
          padding: const EdgeInsets.symmetric(horizontal: 48),
          labelPadding: const EdgeInsets.symmetric(vertical: 16),
          labelColor: AppColor.black,
          labelStyle: Theme.of(context).textTheme.bodyLarge,
          unselectedLabelStyle: Theme.of(context).textTheme.bodySmall,
          indicator: const UnderlineTabIndicator(
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(4),
            ),
            borderSide: BorderSide(
              width: 4,
              color: AppColor.primaryColor,
            ),
            insets: EdgeInsets.symmetric(horizontal: 48),
          ),
          onTap: (index) {
            isSingleUpload = index == 0;
            context.read<TaskCubit>().toggleUploadMode(index == 0);
          },
        ),
      ),
      body: BlocBuilder<TaskCubit, TaskState>(
        builder: (context, state) {
          return state.isSingleUpload
              ? _buildSingleUploadLayout(context, state)
              : _buildSettingLayout();
        },
      ),
    );
  }

  Widget _buildSettingLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: SetupTokenButton(
            onSetupTokenButtonClick: _handleSetupClick,
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              const Text(
                "Cài đặt",
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ValueListenableBuilder<String>(
                valueListenable: MainModule.tokenNotifier,
                builder: (context, token, _) {
                  return Text("Token: ${_getShortToken(token)}",
                      style: const TextStyle(fontSize: 16));
                },
              ),
              const SizedBox(height: 8),
              ValueListenableBuilder<String>(
                valueListenable: MainModule.folderPathNotifier,
                builder: (context, folderPath, _) {
                  return Text("Folder Path: $folderPath",
                      style: const TextStyle(fontSize: 16));
                },
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ],
    );
  }

  String _getShortToken(String token) {
    if (token.length > 12) {
      return "${token.substring(0, 8)}...${token.substring(token.length - 8)}";
    }
    return token; // Trả về nguyên token nếu chiều dài ngắn hơn 12 ký tự
  }

  Widget _buildSingleUploadLayout(BuildContext context, TaskState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: SelectFileButton(
            onFileSelected: (files) {
              isSingleUpload
                  ? _handleSingleUpload(files)
                  : _handleMultipleUpload(files);
            },
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 24),
          child: Text(
            "Task list",
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        state.tasks.isEmpty
            ? _buildEmptyTaskMessage(context)
            : _buildTaskList(state),
      ],
    );
  }

  void upload(XFile xfile) async {
    final uploadFile = UploadFile(
      path: xfile.path,
      name: xfile.name,
      size: 0,
    );

    if (!mounted) return;
    context.read<TaskCubit>().addTaskForSingleUpload(uploadFile);
  }

  void uploads(List<XFile> xfiles) async {
    final List<UploadFile> files = [];

    for (var xfile in xfiles) {
      final uploadFile = UploadFile(
        path: xfile.path,
        name: xfile.name,
        size: await xfile.length(),
      );
      files.add(uploadFile);
    }

    if (!mounted) return;
    context.read<TaskCubit>().addTaskForMultipleUpload(files);
  }

  void _handleMultipleUpload(List<XFile> files) {
    uploads(files);
  }

  void _handleSetupClick() {
    MainModule.fetchAndUpdateToken(context);
  }

  Future<void> _handleSingleUpload(List<XFile> files) async {
    for (final file in files) {
      await Future.delayed(const Duration(milliseconds: 300));
      upload(file);
    }
  }

  Widget _buildEmptyTaskMessage(BuildContext context) {
    return Expanded(
      child: Container(
        alignment: Alignment.center,
        child: Text(
          "No Task",
          style: Theme.of(context).textTheme.bodyMedium,
        ),
      ),
    );
  }

  Widget _buildTaskList(TaskState state) {
    return Expanded(
      child: ListView.separated(
        padding: const EdgeInsets.fromLTRB(32, 0, 32, 32),
        itemCount: state.tasks.length,
        separatorBuilder: (_, __) => const SizedBox(height: 16),
        itemBuilder: (context, index) {
          final task = state.tasks[index];
          return task.type == UploadType.single
              ? SingleUploadItem(key: GlobalObjectKey(task.id), task: task)
              : MultipleTaskItem(key: GlobalObjectKey(task.id), task: task);
        },
      ),
    );
  }
}
