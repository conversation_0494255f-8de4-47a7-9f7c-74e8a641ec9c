import 'package:dio/dio.dart';
import 'package:filestore_sdk/core/implementations/upload_file.dart';

import 'package:filestore_sdk/core/utils/constants/enums.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:example/feature/home/<USER>/upload_task.dart';
import 'package:example/feature/home/<USER>/home_repository.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'single_upload_state.dart';
part 'single_upload_cubit.freezed.dart';

class SingleUploadCubit extends Cubit<SingleUploadState> {
  final HomeRepository repo;

  SingleUploadCubit({
    required this.repo,
    required UploadTask task,
  }) : super(
          SingleUploadState(
            task: task,
            progress: task.status == UploadStatus.success ? 1 : 0,
          ),
        );

  CancelToken? cancelToken;
  void cancelUpload() async {
    cancelToken?.cancel("Canceled");
  }

  void startUpload() async {
    if (state.task.file == null) return;

    try {
      cancelToken = CancelToken();

      //TODO Change it SDK
      await repo.uploadFile(
        state.task.file!,
        success: success,
        progress: progress,
        error: error,
        cancelToken: cancelToken,
      );
    } catch (e) {
      var errorMessage =
          "Something went wrong, please try again! ${e.toString()}";
      emit(state.copyWith(
        errorMessage: errorMessage,
        task: state.task.copyWith(
          status: UploadStatus.failed,
        ),
      ));
    }
  }

  Future<void> resumeUpload() async {
    if (state.task.file == null) return;
    try {
      cancelToken = CancelToken();

      // print("cancelToken $cancelToken");

      //TODO Change it SDK
      await repo.resumeFile(
        state.task.file!,
        success: success,
        progress: progress,
        error: error,
        cancelToken: cancelToken,
      );
    } catch (e) {
      var errorMessage =
          "Something went wrong, please try again! ${e.toString()}";
      emit(state.copyWith(
        errorMessage: errorMessage,
        task: state.task.copyWith(
          status: UploadStatus.failed,
        ),
      ));
    }
  }

  void success(file, fileUrl) {
    // Update status success
    print("File data URL: $fileUrl");
    emit(state.copyWith(
      errorMessage: null,
      task: state.task.copyWith(
        status: UploadStatus.success,
        file: state.task.file?.copyWith(url: fileUrl, size: file.size),
      ),
    ));
  }

  void progress(progress) {
    if (state.task.status != UploadStatus.uploading) {
      emit(state.copyWith(
        task: state.task.copyWith(
          status: UploadStatus.uploading,
        ),
      ));
    }
    emit(state.copyWith(progress: progress));
  }

  void error(UpFile file, ErrorCode errorCode, String message) {
    // Update status error

    switch (errorCode) {
      case ErrorCode.noInternet:
        print(" Update noInternet error! $message");
        print("state.task.status: ${state.task.status}");

        if (state.task.status == UploadStatus.uploading ||
            state.task.status == UploadStatus.init) {
          emit(state.copyWith(
            errorMessage: message,
            task: state.task.copyWith(
              status: UploadStatus.failed,
              file: state.task.file?.copyWith(uploadState: file.uploadState),
            ),
          ));
        }
        break;
      case ErrorCode.uploadError:
        print(" Update error! $message");
        emit(state.copyWith(
          errorMessage: message,
          task: state.task.copyWith(
            status: UploadStatus.failed,
            file: state.task.file?.copyWith(uploadState: file.uploadState),
          ),
        ));
        break;
      case ErrorCode.timeoutListenWebsocket:
        break;
      default:
        emit(state.copyWith(
          errorMessage: "Something went wrong, please try again QQQ! $message",
          task: state.task.copyWith(
            status: UploadStatus.failed,
          ),
        ));
    }
  }
}
