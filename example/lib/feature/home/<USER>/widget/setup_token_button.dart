import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:example/core/theme/app_color.dart';

class SetupTokenButton extends StatelessWidget {
  const SetupTokenButton({
    super.key,
    required this.onSetupTokenButtonClick,
  });

  final dynamic onSetupTokenButtonClick;

  @override
  Widget build(BuildContext context) {
    return DottedBorder(
      strokeWidth: 3,
      dashPattern: const [6, 6],
      strokeCap: StrokeCap.round,
      borderType: BorderType.RRect,
      radius: const Radius.circular(10),
      padding: const EdgeInsets.all(8),
      child: InkWell(
        onTap: () => {onSetupTokenButtonClick()},
        child: Container(
          height: 80,
          decoration: BoxDecoration(
            color: AppColor.primaryColor,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.get_app,
                color: AppColor.white,
                size: 32,
              ),
              const SizedBox(width: 16),
              Text(
                "Setup Token",
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(color: AppColor.white),
              )
            ],
          ),
        ),
      ),
    );
  }
}
