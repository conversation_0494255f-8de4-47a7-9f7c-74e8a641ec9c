import 'package:dio/dio.dart';
import 'package:example/core/network/response/base_response.dart';
import 'package:example/feature/home/<USER>/upload_task.dart';
import 'package:filestore_sdk/core/implementations/upload_file.dart';

import 'package:filestore_sdk/core/utils/constants/enums.dart';

abstract class HomeRepository {
  Future<void> uploadFile(
    UploadFile file, {
    void Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  });

  Future<void> resumeFile(
    UploadFile file, {
    void Function(UpFile objFile, String fileUrl)? success,
    void Function(double progres)? progress,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? error,
    CancelToken? cancelToken,
  });

  Future<BaseResponse<List<String>>> uploadMultipleFiles(
    List<UploadFile> files, {
    void Function(double progres)? progress,
    CancelToken? cancelToken,
  });
}
