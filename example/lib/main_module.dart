import 'package:dio/dio.dart';
import 'package:example/feature/home/<USER>';
import 'package:filestore_sdk/filestore_sdk.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/configs/config.dart';
import 'package:intl/intl.dart';

class MainModule {
  static String token =
      "biiHNXEuP6lpQksoSqrQkK79uffjXlGjSWrdPSy5qfgPxdjtljYyWx8vt4Bez7IKbjWID0BMVvvEA1Kt9F0-og";
  static String folderPath = '/f1811/';
  static const openConnectURL = "api/uploads/open-connection";

  static ValueNotifier<String> tokenNotifier = ValueNotifier<String>(token);
  static ValueNotifier<String> folderPathNotifier =
      ValueNotifier<String>(folderPath);

  static Future<void> init({bool useFilestoreV2 = false}) async {
    await _registerFilestoreClient(useV2: useFilestoreV2);
    _initModule();
  }

  static Future<void> _registerFilestoreClient({bool useV2 = false}) async {
    if (GetIt.I.isRegistered<IFilestoreClient>()) {
      GetIt.I.unregister<IFilestoreClient>();
    }

    final prefs = await SharedPreferences.getInstance();
    token = prefs.getString('token') ?? '';
    folderPath = prefs.getString('folderPath') ?? '';

    if (useV2) {
      GetIt.I.registerSingleton<IFilestoreClient>(
        FilestoreClientV2(
          baseUrl: DemoAppConfig.baseURL,
          token: token,
          folderPath: folderPath,
          openConnectURL: openConnectURL,
          limitFileSizeByMb: 100,
        ),
      );
    } else {
      GetIt.I.registerSingleton<IFilestoreClient>(
        FilestoreClient(
          baseUrl: DemoAppConfig.baseURL,
          token: token,
          folderPath: folderPath,
          openConnectURL: openConnectURL,
          limitFileSizeByMb: 100,
        ),
      );
    }
  }

  static Future<void> updateClient(String newToken, String newFolderPath,
      {bool useFilestoreV2 = false}) async {
    token = newToken;
    folderPath = newFolderPath;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('token', newToken);
    await prefs.setString('folderPath', newFolderPath);

    tokenNotifier.value = newToken;
    folderPathNotifier.value = newFolderPath;

    await _registerFilestoreClient(useV2: useFilestoreV2);
    HomeModule.updateRepository();
  }

  static Future<void> fetchAndUpdateToken(BuildContext context) async {
    try {
      final response = await Dio().post(
        "https://api-dev01.rpc.ziichat.dev/InternalFaker/MockUsers",
        data: {"prefix": "testfake", "quantity": 1, "badge": 0},
      );

      if (response.data['ok'] == true) {
        final dataList = response.data['data'] as List;
        final newToken = dataList[0]['token'] as String;

        final newFolderPath = _getFolderPathByTime();
        updateClient(newToken, newFolderPath, useFilestoreV2: true);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content:
                  Text("Token và FolderPath đã được cập nhật thành công!")),
        );
      } else {
        throw Exception("Không thể lấy thông tin token.");
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Lỗi khi lấy token: $e")),
      );
    }
  }

  static Future<String> fetchAndUpdateFolder(
      BuildContext context, String token) async {
    try {
      String folderPath = _getFolderPathByTime();
      final response = await Dio().post(
        "https://filestore-dev.ziichat.dev/api/folders",
        data: {
          "folderPath": folderPath,
        },
        options: Options(
          headers: {
            "x-session-token": token,
            "Content-Type": "application/json",
          },
        ),
      );

      if (response.data['ok'] == true) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content:
                  Text("FolderPath đã được cập nhật thành công: $folderPath")),
        );

        return folderPath;
      } else {
        throw Exception(
            "Không thể cập nhật folderPath. HTTP status: ${response.statusCode}");
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Lỗi khi cập nhật folderPath: $e")),
      );
    }

    return "";
  }

  static String _getFolderPathByTime() {
    final now = DateTime.now();
    final datePart = DateFormat('ddMM').format(now);
    final timePart = DateFormat('HHmmss').format(now);
    final folderPath = '/f$datePart$timePart/';
    return folderPath;
  }

  static void _initModule() {
    HomeModule.init();
  }
}
