# Tối ưu FilestoreClientV2 - <PERSON><PERSON><PERSON><PERSON> phục lag khi upload chunk

## 🔴 Các vấn đề chính gây lag

### 1. **Memory Management - Vấn đề nghiêm trọng nhất**

**Vấn đề:**
```dart
// Tất cả chunks được load vào memory cùng lúc
for (final chunk in uploadState.uploadChunks!) {
  // Mỗi chunk load toàn bộ data vào memory
  chunk.chunkData = await AppConfig.getChunkData(file, chunk.chunkIndex, uploadState.chunkSize);
}
```

**Hậu quả:** File 100MB = 100+ chunks × 1MB/chunk = >100MB RAM cùng lúc → Lag nghiêm trọng

**Giải pháp:**
```dart
// Chỉ load chunk khi cần gửi, giải phóng ngay sau khi gửi
Future<void> _sendChunkAsync(File file, UploadChunk chunk, ...) async {
  try {
    // Load chunk data chỉ khi cần
    chunk.chunkData = await AppConfig.getChunkData(file, chunk.chunkIndex, uploadState.chunkSize);
    
    // Gửi chunk
    _webSocketManager.sendMessage(...);
    
    // Giải phóng memory ngay lập tức
    chunk.chunkData = null;
  } finally {
    // Đảm bảo memory được giải phóng
    chunk.chunkData = null;
  }
}
```

### 2. **Blocking UI Thread**

**Vấn đề:**
```dart
// Tính MD5 cho tất cả chunks trên main thread
chunk.md5ChunkHash = await AppConfig.calculateMd5ForChunkByIndex(file, chunk.chunkIndex, uploadState.chunkSize);
```

**Giải pháp:**
```dart
// Chuyển sang isolate hoặc compute
Future<String> _calculateMd5InIsolate(File file, int chunkIndex, int chunkSize) async {
  return await compute(_calculateMd5Worker, {
    'filePath': file.path,
    'chunkIndex': chunkIndex,
    'chunkSize': chunkSize,
  });
}

static String _calculateMd5Worker(Map<String, dynamic> params) {
  // Thực hiện tính MD5 trên background thread
  final file = File(params['filePath']);
  // ... logic tính MD5
}
```

### 3. **Inefficient Progress Updates**

**Vấn đề:**
```dart
void _smoothProgress(int start, int end, int totalSize, void Function(double)? onProgress) {
  const int updateSteps = 100; // 100 updates cho mỗi chunk!
  for (int step = 0; step <= updateSteps; step++) {
    Future.delayed(duration, () {
      onProgress?.call((start + step * stepProgress) / totalSize);
    });
  }
}
```

**Hậu quả:** 100 chunks × 100 updates = 10,000 UI updates → Lag UI

**Giải pháp:**
```dart
// Throttle progress updates
DateTime? _lastProgressUpdate;
void _updateProgress(double progress, void Function(double)? onProgress) {
  final now = DateTime.now();
  if (_lastProgressUpdate == null || 
      now.difference(_lastProgressUpdate!).inMilliseconds > 100) { // Update tối đa 10 lần/giây
    onProgress?.call(progress);
    _lastProgressUpdate = now;
  }
}
```

## 🟡 Các vấn đề khác cần tối ưu

### 4. **Concurrent Chunk Management**
```dart
// Vấn đề: Không kiểm soát tốt concurrent chunks
final pool = Pool(AppConfig.maxConcurrentChunks);
// Nhưng vẫn tạo futures cho tất cả chunks cùng lúc

// Giải pháp: Batch processing
Future<void> _uploadChunksInBatches(List<UploadChunk> chunks) async {
  const batchSize = 5; // Upload 5 chunks một lần
  
  for (int i = 0; i < chunks.length; i += batchSize) {
    final batch = chunks.skip(i).take(batchSize).toList();
    await Future.wait(batch.map((chunk) => _sendChunkAsync(chunk)));
    
    // Nhỏ delay để tránh overwhelm
    await Future.delayed(Duration(milliseconds: 10));
  }
}
```

### 5. **WebSocket Message Handling**
```dart
// Vấn đề: Xử lý message trên main thread
messageListener = (String message) {
  Map<String, dynamic> jsonResponse = jsonDecode(message); // Blocking
  // ... xử lý phức tạp
};

// Giải pháp: Xử lý trên background
messageListener = (String message) async {
  final response = await compute(_parseMessageWorker, message);
  _handleParsedMessage(response);
};
```

## 🟢 Code tối ưu hoàn chỉnh

### Optimized _handleFileOver5Mb method:

```dart
void _handleFileOver5Mb(
  File file,
  onProgress,
  onSuccess,
  onError,
  objFile,
  CancelToken? cancelToken,
  String folderPath,
  UploadState uploadState,
) async {
  logDebug("handle File Over 5Mb v2 - Optimized");
  
  final initFileChunkUploadMeta = InitFileChunkUploadMeta(
    type: UploadType.initFileChunkUpload,
    name: basename(file.path),
    totalChunks: uploadState.totalChunks,
    fileSize: uploadState.totalSize,
    ref: uploadState.ref,
  );

  String uploadKey = objFile.key ?? defaultKey;
  
  // Chỉ prepare metadata, không load data
  await _prepareFileChunksMetadataOnly(file, uploadState);
  
  // Sử dụng semaphore thay vì pool để kiểm soát tốt hơn
  final semaphore = Semaphore(AppConfig.maxConcurrentChunks);
  final completedChunks = <int>{};
  
  late final Function(String) messageListener;
  messageListener = (String message) async {
    // Parse message trên background thread
    final response = await compute(_parseWebSocketMessage, message);
    if (response['ref'] != uploadState.ref) return;
    
    await _handleMessageResponse(response, uploadState, objFile, onSuccess, onError, semaphore, completedChunks);
  };

  _webSocketManager.addMessageListener(uploadKey, messageListener);
  _webSocketManager.sendMessage(uploadKey, uploadState.ref ?? '', initFileChunkUploadMeta.toJsonEncode());
}

Future<void> _sendChunkOptimized(
  File file,
  UploadChunk chunk,
  UploadState uploadState,
  String uploadKey,
  Semaphore semaphore,
) async {
  await semaphore.acquire();
  
  try {
    // Load chunk data chỉ khi cần
    final chunkData = await compute(_loadChunkData, {
      'filePath': file.path,
      'chunkIndex': chunk.chunkIndex,
      'chunkSize': uploadState.chunkSize,
    });
    
    final md5Hash = await compute(_calculateMd5, chunkData);
    
    final fileMeta = FileChunkMeta(
      type: UploadType.fileChunkUpload,
      uploadId: uploadState.uploadId!,
      s3UploadId: uploadState.s3UploadId!,
      chunkIndex: chunk.chunkIndex,
      md5ChunkHash: md5Hash,
      chunkData: chunkData,
      ref: uploadState.ref,
    );

    _webSocketManager.sendMessage(uploadKey, fileMeta.ref, fileMeta.toJsonEncode());
    
  } finally {
    semaphore.release();
  }
}

// Background workers
static Uint8List _loadChunkData(Map<String, dynamic> params) {
  final file = File(params['filePath']);
  final chunkIndex = params['chunkIndex'] as int;
  final chunkSize = params['chunkSize'] as int;
  
  final start = chunkIndex * chunkSize;
  final end = math.min(start + chunkSize, file.lengthSync());
  
  return file.readAsBytesSync().sublist(start, end);
}

static String _calculateMd5(Uint8List data) {
  return crypto.md5.convert(data).toString();
}

static Map<String, dynamic> _parseWebSocketMessage(String message) {
  return jsonDecode(message);
}
```

## 📋 Checklist tối ưu

### Urgent (Khắc phục lag ngay):
- [ ] **Lazy load chunk data** - Chỉ load khi cần gửi
- [ ] **Immediate memory cleanup** - Giải phóng chunk.chunkData ngay sau khi gửi
- [ ] **Throttle progress updates** - Tối đa 10 updates/giây
- [ ] **Move MD5 calculation to isolate** - Tránh block UI thread

### Important (Cải thiện performance):
- [ ] **Batch chunk processing** - Upload theo batch thay vì tất cả cùng lúc
- [ ] **WebSocket message parsing optimization** - Sử dụng compute()
- [ ] **Better concurrent control** - Semaphore thay vì Pool
- [ ] **Memory monitoring** - Log memory usage để debug

### Nice to have:
- [ ] **Chunk compression** - Nén data trước khi gửi
- [ ] **Smart retry mechanism** - Retry failed chunks
- [ ] **Upload queue management** - Ưu tiên chunks quan trọng

## 🎯 Kết quả mong đợi sau tối ưu

- **Memory usage giảm 80-90%** (từ >100MB xuống ~10MB cho file 100MB)
- **UI lag giảm đáng kể** (MD5 calculation không block UI)
- **Smoother progress updates** (10 updates/giây thay vì 100+ updates/chunk)
- **Better error handling** và recovery mechanism
