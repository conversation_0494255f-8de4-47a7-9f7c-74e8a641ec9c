import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:uuid/uuid.dart';

class AppConfig {
  static const int chunkSize = 5 * 1024 * 1024;
  static const fiveMb = 1024 * 1024 * 5;

  static const timeoutConnectWebsocketDuration = Duration(seconds: 30);
  static const timeoutListenWebsocketDuration = Duration(seconds: 45);

  static const reconnectIntervalDuration = Duration(seconds: 5);
  static const pingIntervalDuration = Duration(seconds: 30);

  static const int maxConcurrentChunks = 5;

  static const Uuid uuid = Uuid();

  static String generateUniqueId() {
    return uuid.v4(); // VD: '550e8400-e29b-41d4-a716-************'
  }

  static void printDebugMode(String message) {
    if (kDebugMode) {
      print(message);
    }
  }

  static String generateMd5(List<int> bytes) {
    return md5.convert(bytes).toString();
  }

  static Future<String> calculateMd5FromStream(File file) async {
    var digest = await md5.bind(file.openRead()).first;
    return digest.toString();
  }

  static Future<String> calculateMd5ForChunkByIndex(
    File file,
    int chunkIndex,
    int chunkSize,
  ) async {
    final offsetChunk = chunkIndex * chunkSize;
    var digest = await md5
        .bind(
          file.openRead(offsetChunk, offsetChunk + chunkSize),
        )
        .first;
    return digest.toString();
  }

  static Future<Uint8List> getChunkData(
    File file,
    int chunkIndex,
    int chunkSize,
  ) async {
    final offsetChunk = chunkIndex * chunkSize;
    final stream = file.openRead(offsetChunk, offsetChunk + chunkSize);
    final List<int> chunk =
        await stream.fold<List<int>>([], (previous, element) {
      previous.addAll(element);
      return previous;
    });
    return Uint8List.fromList(chunk);
  }
}
