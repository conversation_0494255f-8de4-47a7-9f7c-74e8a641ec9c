enum UploadType {
  initFileUpload("com.halome.file_store.initFileUpload"),
  completeFileUpload("com.halome.file_store.completeFileUpload"),
  initFileChunkUpload("com.halome.file_store.initFileChunkUpload"),
  fileChunkUpload("com.halome.file_store.fileChunkUpload"),
  completeFileChunkUpload("com.halome.file_store.completeFileChunkUpload"),
  getUploadChunks("com.halome.file_store.getUploadChunks"),
  error("error");

  final String value;

  const UploadType(this.value);

  static UploadType? fromString(String type) {
    return UploadType.values.firstWhere(
      (e) => e.value == type,
      orElse: () => UploadType.error,
    );
  }
}

enum ErrorCode { noInternet, unknown, timeoutListenWebsocket, uploadError }
