import 'dart:io';
import 'dart:typed_data';
import 'package:cross_file/cross_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CacheManager {
  final String fileStoreCacheFiles = "fs_cached_files";

  Future<String> getCacheDir() async {
    final dir = await getTemporaryDirectory();
    return dir.path;
  }

  Future<String> saveFileToCacheByData(Uint8List imageBytes) async {
    final cacheDir = await getCacheDir();
    final outputPath =
        '$cacheDir/temp-${DateTime.now().millisecondsSinceEpoch}.png';
    final output = XFile.fromData(imageBytes);
    await output.saveTo(outputPath);
    return outputPath;
  }

  Future<String> saveFileToCache(File file) async {
    final cacheDir = await getCacheDir();
    final cachedFile = File('$cacheDir/${file.path.split('/').last}');
    await file.copy(cachedFile.path);
    await _addFileToCacheList(cachedFile.path);
    return cachedFile.path;
  }

  Future<void> _addFileToCacheList(String filePath) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> cachedFiles = prefs.getStringList(fileStoreCacheFiles) ?? [];
    cachedFiles.add(filePath);
    await prefs.setStringList('cached_files', cachedFiles);
  }

  Future<void> removeFileFromCacheList(String filePath) async {
    final prefs = await SharedPreferences.getInstance();
    List<String> cachedFiles = prefs.getStringList(fileStoreCacheFiles) ?? [];
    if (cachedFiles.contains(filePath)) {
      cachedFiles.remove(filePath);
      await prefs.setStringList('cached_files', cachedFiles);
      final file = File(filePath);
      if (file.existsSync()) {
        file.deleteSync();
      }
    }
  }
}
