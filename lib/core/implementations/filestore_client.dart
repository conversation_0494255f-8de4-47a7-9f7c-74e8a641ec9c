import 'dart:async';
import 'package:dio/dio.dart';
import 'package:mime/mime.dart';
import '../network/http_client_manager.dart';
import '../utils/helpers/cache_manager.dart';
import '../data/models/api_response_classes/complete_file_chunk_meta.dart';
import '../data/models/api_response_classes/complete_file_meta.dart';
import '../data/models/api_response_classes/file_meta.dart';
import '../data/models/api_response_classes/fs_file_chunk_upload_response.dart';
import '../data/models/api_response_classes/fs_get_chunk_uploaded_response.dart';
import '../data/models/api_response_classes/fs_init_file_upload_response.dart';
import '../data/models/api_response_classes/get_file_chunk_meta.dart';
import '../data/models/api_response_classes/init_file_chunk_upload_meta.dart';
import '../utils/constants/config.dart';
import '../data/models/api_response_classes/file_chunk_meta.dart';
import '../data/models/api_response_classes/fs_base_response.dart';
import '../utils/exceptions/file_store_exception.dart';
import '../interfaces/filestore_client_interface.dart';
import '../network/fs_network_manager.dart';
import 'upload_file.dart';

import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/io.dart';
import 'package:path/path.dart';

import 'upload_state.dart';
import '../utils/constants/enums.dart';

class FilestoreClient implements IFilestoreClient {
  final HttpClientManager httpClientManager;
  late final Dio client;
  final String baseUrl;
  final String? token;
  final String? folderPath;
  final String openConnectURL;

  final FSNetworkManager fsNetworkManager = FSNetworkManager();
  late int limitFileSizeByMb;
  late List<String>? limitFileType;

  final CacheManager cacheManager = CacheManager();

  void debug(String message) => AppConfig.printDebugMode(message);

  FilestoreClient({
    required this.baseUrl,
    this.token,
    this.folderPath,
    required this.openConnectURL,
    int? limitFileSizeByMb,
    this.limitFileType,
  }) : httpClientManager = HttpClientManager(baseUrl: baseUrl, token: token) {
    client = httpClientManager.dio;
    this.limitFileSizeByMb = limitFileSizeByMb ?? 0;
  }

  /// Uploads a file to the server using a WebSocket connection for files under 5 MB.
  /// For files larger than 5 MB, the function uses `handleFileOver5Mb` for chunked uploads.
  ///
  /// This function initiates a WebSocket connection using a URL from `getConnectUrl`, prepares the file data,
  /// and sends it in one transmission for small files. If the upload times out (no message received within the
  /// specified duration), it closes the WebSocket and completes with an error message. The function listens to
  /// WebSocket messages to handle the upload status and to complete or return errors as `BaseResponse`.
  ///
  /// Parameters:
  /// - `objFile`: The `UpFile` object representing the file to upload.
  /// - `progress`: A callback function to report upload progress as a percentage.
  /// - `cancelToken`: A token to cancel the upload request if needed.
  ///
  /// Returns:
  /// - `Future<FileUploadResponse>`: A future that completes with a `BaseResponse` containing the upload result.
  ///   If successful, the `data` contains the file URL; if an error occurs, the `message` describes the error.
  ///
  /// Error Handling:
  /// - Uses `timeoutListenWebsocketDuration` to limit waiting time for messages from the server.
  /// - On timeout or errors during the upload, completes with `FileUploadResponse.error`.
  /// - If the file exceeds 5 MB, `handleFileOver5Mb` is used to handle chunked uploading.
  ///

  @override
  Future<void> uploadFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  }) async {
    try {
      debug("FileStore - SDK Start!!");
      String filePath = "";
      if (objFile.fileData != null) {
        filePath = await cacheManager.saveFileToCacheByData(objFile.fileData!);
      } else {
        filePath = objFile.path;
      }
      File file = File(filePath);
      int fileSize = file.lengthSync();
      String? fileType = lookupMimeType(file.path);
      objFile.size = fileSize;
      token = token ?? this.token;
      folderPath = folderPath ?? this.folderPath;
      if (_isNotValidFileType(fileType)) {
        onError?.call(objFile, ErrorCode.uploadError, "Invalid file type");
      } else if (_isLimitExceededFileSize(fileSize)) {
        onError?.call(
          objFile,
          ErrorCode.uploadError,
          "File upload limit exceeded ${limitFileSizeByMb}MB",
        );
      } else if (fileSize < AppConfig.fiveMb) {
        await _handleFileSmaller5Mb(
          file,
          onError,
          objFile,
          cancelToken,
          onSuccess,
          token!,
          folderPath!,
        );
      } else {
        _handleFileOver5Mb(
          file,
          onProgress,
          onSuccess,
          onError,
          objFile,
          cancelToken,
          token!,
          folderPath!,
        );
      }
    } catch (e) {
      if (e is SocketException) {
        onError?.call(objFile, ErrorCode.uploadError, e.message);
      } else {
        onError?.call(objFile, ErrorCode.unknown, e.toString());
      }
    }
  }

  void _listenConnectivityChanged(
    File file,
    IOWebSocketChannel channel,
    dynamic onError,
    UpFile objFile,
    UploadState? uploadState,
  ) {
    fsNetworkManager.onConnectivityChanged.listen((result) {
      debug("Connection changed $result");
      if (fsNetworkManager.isNone) {
        debug("Disconnected. Pausing upload...");

        _handleUploadError(
          file,
          objFile,
          uploadState,
          onError,
          ErrorCode.noInternet,
          "Device is disconnect. Close upload.",
          channel,
        );
      }
    });
  }

  void _listenUploadCanceledByUser(
    File file,
    IOWebSocketChannel channel,
    dynamic onError,
    UpFile objFile,
    UploadState? uploadState,
  ) {
    _handleUploadError(
      file,
      objFile,
      uploadState,
      onError,
      ErrorCode.noInternet,
      "Upload canceled by user.. Close upload.",
      channel,
    );
  }

  Future<String> _getConnectUrl(String token, String folderPath) async {
    try {
      Response response = await client.get(
        openConnectURL,
        queryParameters: {'folderPath': folderPath},
        options: Options(
          headers: {
            "x-session-token": token,
          },
        ),
      );

      final fsBaseResponse = FSBaseResponse<FSData>.fromJson(
        jsonDecode(response.toString()),
        (json) => FSData.fromJson(json as Map<String, dynamic>),
      );

      debug("Received URL: ${fsBaseResponse.data?.url}");
      return fsBaseResponse.data?.url ?? "";
    } catch (e) {
      if (e is DioException) {
        String msg = "";
        switch (e.type) {
          case DioExceptionType.connectionTimeout:
          case DioExceptionType.sendTimeout:
          case DioExceptionType.receiveTimeout:
            msg = "Timeout occurred while sending or receiving";
          case DioExceptionType.cancel:
            break;
          case DioExceptionType.unknown:
            msg = "No Internet Connection";
            break;
          case DioExceptionType.badCertificate:
            msg = "Internal Server Error";
            break;
          case DioExceptionType.connectionError:
            msg = "Connection Error";
            break;
          default:
            msg = "Unknown Error";
            break;
        }

        if (e.response != null) {
          final fsBaseResponse = FSBaseResponse<FSData>.fromJson(
            jsonDecode(e.response.toString()),
            (json) => FSData.fromJson(json as Map<String, dynamic>),
          );

          final errorMessage = fsBaseResponse.error?.message;
          final code = fsBaseResponse.error?.code;
          msg = "Error: ${code ?? 'Unknown status code'} - $errorMessage";
        }
        throw FileStoreException(msg);
      } else {
        // Xử lý các lỗi khác ngoài DioException
        debug("Unexpected error: $e");
        throw FileStoreException("Unexpected error occurred: $e");
      }
    }
  }

  @override
  Future<void> resumedFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  }) async {
    objFile.uploadState;

    late DateTime startTime, endTime;
    try {
      folderPath = folderPath ?? this.folderPath;
      token = token ?? this.token;

      debug(
        "Resume cachedFilePath: ${objFile.uploadState!.cachedFilePath}",
      );
      File file = File(objFile.uploadState!.cachedFilePath ?? objFile.path);

      final fileBytes = await file.readAsBytes();
      final fileSize = file.lengthSync();
      objFile.size = fileSize;

      if (fileSize < AppConfig.fiveMb) {
        _handleFileSmaller5Mb(
          file,
          onError,
          objFile,
          cancelToken,
          onSuccess,
          token!,
          folderPath!,
        );
      } else {
        debug(
          "Resume chunkIndex: ${objFile.uploadState?.chunkIndex}",
        );

        final uploadState = objFile.uploadState!;
        final connectUrl = await _getConnectUrl(token!, folderPath!);
        debug(uploadState.uploadChunks!.last.toString());

        final channel = await _connectWithTimeout(
          connectUrl,
          AppConfig.timeoutConnectWebsocketDuration,
          headers: {
            "x-session-token": token,
          },
        );

        await channel.ready;
        uploadState.webSocketUrl = connectUrl;
        _listenConnectivityChanged(
          file,
          channel,
          onError,
          objFile,
          uploadState,
        );
        cancelToken?.whenCancel.then((_) {
          debug("Upload canceled by user.");
          _listenUploadCanceledByUser(
            file,
            channel,
            onError,
            objFile,
            uploadState,
          );
        });

        final getFileChunkMeta = GetFileChunkMeta(
          type: UploadType.getUploadChunks,
          uploadId: uploadState.uploadId!,
        );
        debug("Resume - getUploadChunks: ${uploadState.toString()}");
        channel.sink.add(getFileChunkMeta.toJsonEncode());

        channel.stream.timeout(
          AppConfig.timeoutListenWebsocketDuration,
          onTimeout: (sink) {
            debug("Timeout: No message received within 30 seconds.");
            onError?.call(
              objFile,
              ErrorCode.timeoutListenWebsocket,
              "Timeout: No message received within 30 seconds.",
            );

            sink.close();
          },
        ).listen(
          (msg) async {
            debug("msg: $msg");
            Map<String, dynamic> jsonResponse = jsonDecode(msg);

            final response = FSFileChunkUploadResponse.fromJson(jsonResponse);
            final actionType = UploadType.fromString(response.type);

            switch (actionType) {
              case UploadType.getUploadChunks:
                final getChunkUploadedData =
                    FSGetChunkUploadedData.fromJson(response.data);

                if (getChunkUploadedData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${getChunkUploadedData.error?.message}: ${getChunkUploadedData.error?.details}",
                    channel,
                  );
                } else {
                  List uploadChunks = getChunkUploadedData.data!;

                  final lastChunk = uploadChunks
                      .where((uc) => uc is FSUploadChunk && uc.isCompleted)
                      .cast<FSUploadChunk>()
                      .lastOrNull;

                  uploadState.chunkIndex =
                      lastChunk == null ? 0 : lastChunk.chunkIndex + 1;

                  uploadState.offset = 0;
                  if (lastChunk != null) {
                    final lastChunkClient = uploadState.uploadChunks!
                        .where((c) => c.chunkIndex == lastChunk.chunkIndex)
                        .singleOrNull;
                    if (lastChunkClient != null) {
                      uploadState.offset =
                          lastChunkClient.offset + AppConfig.chunkSize;
                    }
                  }

                  startTime = DateTime.now();
                  debug("Resume at data: ${uploadState.toString()}");

                  _uploadFileChunk(uploadState, fileBytes, channel, onProgress);
                }

                break;
              case UploadType.fileChunkUpload:
                final fileChunkUploadData =
                    FSFileChunkUploadData.fromJson(response.data);

                if (fileChunkUploadData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
                    channel,
                  );
                } else {
                  if (uploadState.offset == AppConfig.chunkSize) {
                    endTime = DateTime.now();
                    uploadState.setUploadSpeed(startTime, endTime);
                  }

                  List uploadChunks = fileChunkUploadData.data!.uploadChunks;
                  uploadState.uploadChunks?.last.isCompleted = true;
                  if (uploadChunks.length < uploadState.totalChunks) {
                    _uploadFileChunk(
                      uploadState,
                      fileBytes,
                      channel,
                      onProgress,
                    );
                  } else if (uploadChunks
                          .where((uc) => uc is FSUploadChunk && uc.isCompleted)
                          .cast<FSUploadChunk>()
                          .length ==
                      uploadState.totalChunks) {
                    // completeFileChunkUpload
                    final completeFileChunkMeta = CompleteFileChunkMeta(
                      type: UploadType.completeFileChunkUpload,
                      uploadId: uploadState.uploadId!,
                      s3UploadId: uploadState.s3UploadId!,
                    );
                    channel.sink.add(completeFileChunkMeta.toJsonEncode());
                  }
                }
                break;
              case UploadType.completeFileChunkUpload:
                final completeFileChunkData =
                    FSCompleteFileChunkData.fromJson(response.data);
                if (completeFileChunkData.ok == false) {
                  _handleUploadError(
                    file,
                    objFile,
                    uploadState,
                    onError,
                    ErrorCode.uploadError,
                    "${completeFileChunkData.error?.message}: ${completeFileChunkData.error?.details}",
                    channel,
                  );
                } else {
                  final s3Url = completeFileChunkData.data?.s3Url;
                  if (uploadState.cachedFilePath != null) {
                    cacheManager
                        .removeFileFromCacheList(uploadState.cachedFilePath!);
                  }
                  channel.sink.close();
                  objFile.metaData = completeFileChunkData.data!;
                  onSuccess?.call(objFile, s3Url!);
                }
                break;
              default:
            }
          },
          onError: (error) {
            onError?.call(objFile, ErrorCode.unknown, error.toString());
          },
        );
      }
    } catch (e) {
      debug("Expectation caught: $e");
      onError?.call(objFile, ErrorCode.unknown, e.toString());
    }
  }

  // Private Upload

  Future<void> _handleFileSmaller5Mb(
    File file,
    onError,
    UpFile objFile,
    CancelToken? cancelToken,
    onSuccess,
    String token,
    String folderPath,
  ) async {
    final connectUrl = await _getConnectUrl(token, folderPath);

    final channel = await _connectWithTimeout(
      connectUrl,
      AppConfig.timeoutConnectWebsocketDuration,
      headers: {
        "x-session-token": token,
      },
    );

    await channel.ready;
    debug("channel.ready!!!");

    final uploadState = UploadState(
      offset: 0,
      chunkIndex: 0,
      totalSize: file.lengthSync(),
      chunkSize: AppConfig.chunkSize,
      cachedFilePath: file.path,
    );

    _listenConnectivityChanged(file, channel, onError, objFile, uploadState);
    cancelToken?.whenCancel.then((_) {
      debug("Upload canceled by user.");
      _listenUploadCanceledByUser(file, channel, onError, objFile, uploadState);
    });

    Uint8List bytes = objFile.fileData != null
        ? objFile.fileData as Uint8List
        : await file.readAsBytes();
    final fileMeta = FileMeta(
      type: UploadType.initFileUpload,
      name: basename(file.path),
      md5FileHash: AppConfig.generateMd5(bytes),
      fileData: bytes,
    );

    channel.sink.add(fileMeta.toJsonEncode());

    channel.stream.timeout(
      AppConfig.timeoutListenWebsocketDuration,
      onTimeout: (sink) {
        debug("Timeout: No message received within 30 seconds.");
        onError?.call(
          objFile,
          ErrorCode.timeoutListenWebsocket,
          "Timeout: No message received within 30 seconds.",
        );
        sink.close();
      },
    ).listen(
      (msg) {
        debug("msg: $msg");

        Map<String, dynamic> jsonResponse = jsonDecode(msg);
        final response = FSInitFileUploadResponse.fromJson(jsonResponse);

        if (response.type == UploadType.initFileUpload.value) {
          if (response.data.ok == false) {
            onError?.call(
              objFile,
              ErrorCode.uploadError,
              "${response.data.error?.message}: ${response.data.error?.details}",
            );
          } else {
            final uploadId = response.data.data?.uploadId;
            final completeFileMeta = CompleteFileMeta(
              type: UploadType.completeFileUpload,
              uploadId: uploadId!,
            );
            channel.sink.add(completeFileMeta.toJsonEncode());
          }
        } else if (response.type == UploadType.completeFileUpload.value) {
          if (response.data.ok == false) {
            onError?.call(
              objFile,
              ErrorCode.uploadError,
              "${response.data.error?.message}: ${response.data.error?.details}",
            );
          } else {
            final s3Url = response.data.data?.s3Url;
            if (uploadState.cachedFilePath != null) {
              cacheManager.removeFileFromCacheList(uploadState.cachedFilePath!);
            }
            channel.sink.close();
            objFile.metaData = response.data.data!;
            onSuccess?.call(objFile, s3Url!);
          }
        }
      },
      onError: (error) {
        error.call(objFile, ErrorCode.uploadError, error.toString());
      },
    );
  }

  /// Handles file upload for files larger than 5MB by uploading them in chunks.
  ///
  /// This function is responsible for managing the upload of files larger than 5MB by breaking them into smaller chunks and sending them via WebSocket.
  /// It listens for messages from the server to control the flow of the upload and handles timeouts, errors, and chunk uploads.
  ///
  /// The function operates as follows:
  /// 1. Retrieves the connection URL for the upload.
  /// 2. Reads the file to be uploaded into memory.
  /// 3. Initializes the WebSocket connection and sends an initial request to start chunked upload.
  /// 4. Sends chunks of the file one by one, ensuring the upload state is correctly maintained.
  /// 5. If all chunks are uploaded, it sends a request to complete the file upload.
  /// 6. Handles server responses to control upload flow and retries if necessary.
  /// 7. Handles timeout and error conditions for the WebSocket connection.
  ///
  /// **Parameters:**
  ///
  /// - `folderPath` (String): The path to the folder where the file should be uploaded.
  /// - `file` (File): The file to be uploaded. The file must be larger than 5MB.
  /// - `completer` (Completer<FileUploadResponse>): A `Completer` that completes the upload process. It is used to return either the successful result or any error encountered during the upload process.
  /// - `progress` (Function? progress): A callback function to report the upload progress as chunks are uploaded. The progress function receives a `double` value representing the progress (0 to 1).
  ///
  /// **Returns:**
  /// - This function does not return anything directly. The `completer` will complete with either a `BaseResponse` indicating success or failure.
  ///
  /// **Error Handling:**
  /// - In case of any error during the upload (e.g., connection issues, server errors), the function will call `completer.completeError` with a relevant error message.
  /// - If no message is received within the specified timeout, the function will notify the caller with a timeout error.
  ///
  /// **Notes:**
  /// - This function specifically handles file uploads for files larger than 5MB by splitting the file into smaller chunks for efficient upload.
  /// - The `progress` callback is used to report the upload progress to the caller as chunks are uploaded.
  ///
  void _handleFileOver5Mb(
    File file,
    onProgress,
    onSuccess,
    onError,
    objFile,
    CancelToken? cancelToken,
    String token,
    String folderPath,
  ) async {
    debug("handle File Over 5Mb");
    late DateTime startTime, endTime;
    try {
      token = token;
      final connectUrl = await _getConnectUrl(token, folderPath);

      final fileBytes = await file.readAsBytes();

      final uploadState = UploadState(
        offset: 0,
        chunkIndex: 0,
        totalSize: fileBytes.length,
        chunkSize: AppConfig.chunkSize,
      );

      final channel = await _connectWithTimeout(
        connectUrl,
        AppConfig.timeoutConnectWebsocketDuration,
        headers: {
          "x-session-token": token,
        },
      );

      await channel.ready;
      uploadState.webSocketUrl = connectUrl;
      cancelToken?.whenCancel.then((_) {
        debug("Upload canceled by user.");
        _listenUploadCanceledByUser(
          file,
          channel,
          onError,
          objFile,
          uploadState,
        );
      });

      final initFileChunkUploadMeta = InitFileChunkUploadMeta(
        type: UploadType.initFileChunkUpload,
        name: basename(file.path),
        md5FileHash: AppConfig.generateMd5(fileBytes),
        totalChunks: uploadState.totalChunks,
        fileSize: uploadState.totalSize,
      );

      _listenConnectivityChanged(file, channel, onError, objFile, uploadState);
      channel.sink.add(initFileChunkUploadMeta.toJsonEncode());

      channel.stream.timeout(
        AppConfig.timeoutListenWebsocketDuration,
        onTimeout: (sink) {
          debug("Timeout: No message received within 30 seconds.");
          onError?.call(
            objFile,
            ErrorCode.timeoutListenWebsocket,
            "Timeout: No message received within 30 seconds.",
          );

          sink.close();
        },
      ).listen(
        (msg) async {
          debug("msg: $msg");

          Map<String, dynamic> jsonResponse = jsonDecode(msg);

          final response = FSFileChunkUploadResponse.fromJson(jsonResponse);
          final actionType = UploadType.fromString(response.type);

          switch (actionType) {
            case UploadType.initFileChunkUpload:
              final fileChunkUploadData =
                  FSFileChunkUploadData.fromJson(response.data);
              uploadState.uploadId = fileChunkUploadData.data?.uploadId;
              uploadState.s3UploadId = fileChunkUploadData.data?.s3UploadId;
              if (fileChunkUploadData.ok == false) {
                onError?.call(
                  objFile,
                  ErrorCode.uploadError,
                  "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
                );
              } else {
                startTime = DateTime.now();
                _uploadFileChunk(uploadState, fileBytes, channel, onProgress);
              }
              break;
            case UploadType.fileChunkUpload:
              final fileChunkUploadData =
                  FSFileChunkUploadData.fromJson(response.data);

              if (fileChunkUploadData.ok == false) {
                _handleUploadError(
                  file,
                  objFile,
                  uploadState,
                  onError,
                  ErrorCode.uploadError,
                  "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
                  channel,
                );
              } else {
                final uploadChunk = fileChunkUploadData.data!.uploadChunk;
                if (uploadChunk != null && uploadChunk.isCompleted == true) {
                  uploadState.uploadChunks
                      ?.where((uc) => uc.chunkIndex == uploadChunk.chunkIndex)
                      .single
                      .isCompleted = true;
                }

                final part = uploadChunk?.partNumber ?? 0;
                if (part < uploadState.totalChunks) {
                  _uploadFileChunk(uploadState, fileBytes, channel, onProgress);
                } else if (part == uploadState.totalChunks) {
                  // completeFileChunkUpload
                  final completeFileChunkMeta = CompleteFileChunkMeta(
                    type: UploadType.completeFileChunkUpload,
                    uploadId: uploadState.uploadId!,
                    s3UploadId: uploadState.s3UploadId!,
                  );
                  channel.sink.add(completeFileChunkMeta.toJsonEncode());
                }
              }
              break;
            case UploadType.completeFileChunkUpload:
              final completeFileChunkData =
                  FSCompleteFileChunkData.fromJson(response.data);
              if (completeFileChunkData.ok == false) {
                _handleUploadError(
                  file,
                  objFile,
                  uploadState,
                  onError,
                  ErrorCode.uploadError,
                  "${completeFileChunkData.error?.message}: ${completeFileChunkData.error?.details}",
                  channel,
                );
              } else {
                final s3Url = completeFileChunkData.data?.s3Url;
                channel.sink.close();
                if (uploadState.cachedFilePath != null) {
                  cacheManager
                      .removeFileFromCacheList(uploadState.cachedFilePath!);
                }
                objFile.metaData = completeFileChunkData.data;
                onSuccess?.call(objFile, s3Url!);

                endTime = DateTime.now();
                uploadState.setUploadSpeed(startTime, endTime);
              }
              break;
            default:
          }
        },
        onError: (error) {
          onError?.call(objFile, ErrorCode.unknown, error.toString());
        },
      );
    } catch (e) {
      debug("Expectation caught: $e");
      onError?.call(objFile, ErrorCode.unknown, e.toString());
    }
  }

  void _uploadFileChunk(
    UploadState uploadState,
    fileBytes,
    IOWebSocketChannel channel,
    onProgress,
  ) {
    debug("offset: ${uploadState.offset}");

    final chunk = fileBytes.sublist(uploadState.offset, uploadState.end);

    final fileMeta = FileChunkMeta(
      type: UploadType.fileChunkUpload,
      uploadId: uploadState.uploadId!,
      s3UploadId: uploadState.s3UploadId!,
      chunkIndex: uploadState.chunkIndex,
      md5ChunkHash: AppConfig.generateMd5(chunk),
      chunkData: chunk,
    );

    channel.sink.add(fileMeta.toJsonEncode());

    final newChunk = UploadChunk(
      offset: uploadState.offset,
      chunkIndex: fileMeta.chunkIndex,
      md5ChunkHash: fileMeta.md5ChunkHash,
      isCompleted: false,
    );

    if (uploadState.uploadChunks!
        .where((chunk) => chunk.chunkIndex == fileMeta.chunkIndex)
        .isEmpty) {
      uploadState.uploadChunks?.add(newChunk);
    }

    debug(
      'Chunk ${uploadState.chunkIndex} of ${uploadState.totalChunks} sent: ${chunk.length} bytes. '
      'Progress: ${uploadState.chunkIndex / uploadState.totalSize} chunks: ${uploadState.uploadChunks.toString()}',
    );

    _smoothProgress(
      uploadState.offset,
      uploadState.end,
      uploadState.totalSize,
      onProgress,
    );

    uploadState.offset = uploadState.end;
    uploadState.chunkIndex++;
  }

  // Private connection

  Future<IOWebSocketChannel> _connectWithTimeout(
    String url,
    Duration timeout, {
    Map<String, dynamic>? headers,
  }) async {
    try {
      // Use Future.any to set a timeout for the connection process
      final channel = await Future.any([
        Future(
          () async => IOWebSocketChannel.connect(
            Uri.parse(url),
            headers: headers,
            pingInterval: AppConfig.pingIntervalDuration,
          ),
        ),
        Future.delayed(
          timeout,
          () => throw TimeoutException('Connection to $url timed out'),
        ),
      ]);
      return channel;
    } on TimeoutException {
      debug("Connection to WebSocket timed out.");
      rethrow; // Optional: Handle the exception here if desired
    } catch (e) {
      debug("Error connecting to WebSocket: $e");
      rethrow;
    }
  }

  // Private helper / handleError / valid

  Future<void> _handleUploadError(
    File file,
    UpFile objFile,
    UploadState? uploadState,
    onError,
    errorCode,
    errorMessage,
    IOWebSocketChannel channel,
  ) async {
    if (uploadState != null) {
      uploadState.cachedFilePath = await cacheManager.saveFileToCache(file);
      objFile.uploadState = uploadState;

      debug("Error Index chunk: ${uploadState.toString()}.");
    }

    onError.call(objFile, errorCode, errorMessage);
    channel.sink.close();
    debug("Channel closed!!!!!! ${uploadState?.webSocketUrl}");
  }

  bool _isLimitExceededFileSize(fileSize) {
    return limitFileSizeByMb == 0
        ? false
        : fileSize > limitFileSizeByMb * 1024 * 1024;
  }

  bool _isNotValidFileType(fileType) {
    return limitFileType is List<String>
        ? !limitFileType!.contains(fileType)
        : false;
  }

  void _smoothProgress(
    int start,
    int end,
    int totalSize,
    void Function(double)? onProgress,
  ) {
    if (onProgress == null) return;

    const int updateSteps = 100;
    const int delayMultiplier = 5;

    final double stepProgress = (end - start) / updateSteps;

    for (int step = 0; step <= updateSteps; step++) {
      final duration = Duration(milliseconds: delayMultiplier * step);
      Future.delayed(duration, () {
        onProgress.call((start + step * stepProgress) / totalSize);
      });
    }
  }
}
