import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:web_socket_channel/io.dart';
import 'package:path/path.dart';
import 'package:dio/dio.dart';
import 'package:mime/mime.dart';
import 'package:pool/pool.dart';

import '../network/web_socket/web_socket_state.dart';
import 'upload_file.dart';
import 'upload_state.dart';
import '../network/http_client_manager.dart';
import '../network/web_socket/web_socket_manager.dart';
import '../utils/helpers/cache_manager.dart';
import '../data/models/api_response_classes/complete_file_chunk_meta.dart';
import '../data/models/api_response_classes/complete_file_meta.dart';
import '../data/models/api_response_classes/file_meta.dart';
import '../data/models/api_response_classes/fs_file_chunk_upload_response.dart';
import '../data/models/api_response_classes/fs_get_chunk_uploaded_response.dart';
import '../data/models/api_response_classes/fs_init_file_upload_response.dart';
import '../data/models/api_response_classes/get_file_chunk_meta.dart';
import '../data/models/api_response_classes/init_file_chunk_upload_meta.dart';
import '../utils/constants/config.dart';
import '../data/models/api_response_classes/file_chunk_meta.dart';
import '../data/models/api_response_classes/fs_base_response.dart';
import '../utils/exceptions/file_store_exception.dart';
import '../interfaces/filestore_client_interface.dart';
import '../network/fs_network_manager.dart';
import '../utils/constants/enums.dart';

class FilestoreClientV2 implements IFilestoreClient {
  final HttpClientManager httpClientManager;
  late final Dio client;
  final String baseUrl;
  final String? token;
  final String? folderPath;
  final String openConnectURL;
  late int limitFileSizeByMb;
  late List<String>? limitFileType;

  void logDebug(String message) => AppConfig.printDebugMode(message);

  final FSNetworkManager fsNetworkManager = FSNetworkManager();
  final CacheManager cacheManager = CacheManager();

  final WebSocketManager _webSocketManager = WebSocketManager();

  final String defaultKey = "ws_${DateTime.now().millisecondsSinceEpoch}";

  FilestoreClientV2({
    required this.baseUrl,
    this.token,
    this.folderPath,
    required this.openConnectURL,
    int? limitFileSizeByMb,
    this.limitFileType,
  }) : httpClientManager = HttpClientManager(baseUrl: baseUrl, token: token) {
    client = httpClientManager.dio;
    this.limitFileSizeByMb = limitFileSizeByMb ?? 0;
  }

  /// Uploads a file to the server using a WebSocket connection for files under 5 MB.
  /// For files larger than 5 MB, the function uses `handleFileOver5Mb` for chunked uploads.
  ///
  /// This function initiates a WebSocket connection using a URL from `getConnectUrl`, prepares the file data,
  /// and sends it in one transmission for small files. If the upload times out (no message received within the
  /// specified duration), it closes the WebSocket and completes with an error message. The function listens to
  /// WebSocket messages to handle the upload status and to complete or return errors as `BaseResponse`.
  ///
  /// Parameters:
  /// - `objFile`: The `UpFile` object representing the file to upload.
  /// - `progress`: A callback function to report upload progress as a percentage.
  /// - `cancelToken`: A token to cancel the upload request if needed.
  ///
  /// Returns:
  /// - `Future<FileUploadResponse>`: A future that completes with a `BaseResponse` containing the upload result.
  ///   If successful, the `data` contains the file URL; if an error occurs, the `message` describes the error.
  ///
  /// Error Handling:
  /// - Uses `timeoutListenWebsocketDuration` to limit waiting time for messages from the server.
  /// - On timeout or errors during the upload, completes with `FileUploadResponse.error`.
  /// - If the file exceeds 5 MB, `handleFileOver5Mb` is used to handle chunked uploading.
  ///

  @override
  Future<void> uploadFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    Future<String?> Function()? getTokenCallback,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  }) async {
    logDebug("Uploading file: ${objFile.name}");
    String filePath = "";

    /// Config upload file by fileData Uint8List
    if (objFile.fileData != null) {
      filePath = await cacheManager.saveFileToCacheByData(objFile.fileData!);
    } else {
      filePath = objFile.path;
    }

    File file = File(filePath);
    int fileSize = file.lengthSync();
    String? fileType = lookupMimeType(file.path);
    objFile.size = fileSize;
    token = token ?? this.token;
    folderPath = folderPath ?? this.folderPath;

    final String uploadKey = objFile.key ?? defaultKey;
    final String uploadRef = objFile.ref ?? AppConfig.generateUniqueId();
    late WebSocketState? wsState;

    if (uploadKey.isEmpty) {
      onError?.call(objFile, ErrorCode.uploadError, "Invalid file key");
      return;
    }

    if (_isNotValidFileType(fileType)) {
      onError?.call(objFile, ErrorCode.uploadError, "Invalid file type");
      return;
    }

    if (_isLimitExceededFileSize(fileSize)) {
      onError?.call(
        objFile,
        ErrorCode.uploadError,
        "File upload limit exceeded ${limitFileSizeByMb}MB",
      );
      return;
    }

    Future<String> getConnectUrl() async => await _getConnectUrl(
          token ?? '',
          folderPath ?? '',
          getTokenCallback,
        );

    /// Check objFile.key
    /// If _webSocketManager.isConnected & key == last key, use ws exited. Else, create new connection.
    ///
    wsState = await _webSocketManager.connect(
      uploadKey,
      uploadRef,
      getConnectUrl,
      headers: {"Authorization": "Bearer $token"},
    );

    final uploadState = UploadState(
      offset: 0,
      chunkIndex: 0,
      totalSize: file.lengthSync(),
      chunkSize: AppConfig.chunkSize,
      cachedFilePath: file.path,
      wsState: wsState,
      ref: uploadRef,
    );

    try {
      /// Send file small
      if (fileSize < AppConfig.fiveMb) {
        await _handleFileSmaller5Mb(
          file,
          onError,
          objFile,
          cancelToken,
          onSuccess,
          folderPath!,
          uploadState,
        );
        return;
      }

      /// Send file over 5mb
      _handleFileOver5Mb(
        file,
        onProgress,
        onSuccess,
        onError,
        objFile,
        cancelToken,
        folderPath!,
        uploadState,
      );
    } catch (e) {
      logDebug("Error during WebSocket upload: $e");
      if (e is SocketException) {
        onError?.call(objFile, ErrorCode.uploadError, e.message);
      } else {
        onError?.call(objFile, ErrorCode.unknown, e.toString());
      }
    }
  }

{{ ... }}

  /// Handles file upload for files larger than 5MB by uploading them in chunks.
  ///
  /// This function is responsible for managing the upload of files larger than 5MB by breaking them into smaller chunks and sending them via WebSocket.
  /// It listens for messages from the server to control the flow of the upload and handles timeouts, errors, and chunk uploads.
  ///
  /// The function operates as follows:
  /// 1. Retrieves the connection URL for the upload.
  /// 2. Prepares metadata for file chunks without loading all data into memory.
  /// 3. Initializes the WebSocket connection and sends an initial request to start chunked upload.
  /// 4. Uses StreamController with back-pressure to send chunks one by one, ensuring memory efficiency.
  /// 5. If all chunks are uploaded, it sends a request to complete the file upload.
  /// 6. Handles server responses to control upload flow and retries if necessary.
  /// 7. Handles timeout and error conditions for the WebSocket connection.
  ///
  /// **Parameters:**
  ///
  /// - `folderPath` (String): The path to the folder where the file should be uploaded.
  /// - `file` (File): The file to be uploaded. The file must be larger than 5MB.
  /// - `progress` (Function? progress): A callback function to report the upload progress as chunks are uploaded.
  ///
  void _handleFileOver5Mb(
    File file,
    onProgress,
    onSuccess,
    onError,
    objFile,
    CancelToken? cancelToken,
    String folderPath,
    UploadState uploadState,
  ) async {
    logDebug("handle File Over 5Mb v2");
    late DateTime startTime, endTime;

    cancelToken?.whenCancel.then((_) {
      logDebug("Upload canceled by user.");
      _listenUploadCanceledByUser(
        file,
        onError,
        objFile,
        uploadState,
      );
    });

    final initFileChunkUploadMeta = InitFileChunkUploadMeta(
      type: UploadType.initFileChunkUpload,
      name: basename(file.path),
      totalChunks: uploadState.totalChunks,
      fileSize: uploadState.totalSize,
      ref: uploadState.ref,
    );

    String uploadKey = objFile.key ?? defaultKey;
    
    // Chỉ chuẩn bị metadata cho chunks, không load dữ liệu
    await _prepareFileChunksLite(file, uploadState);

    // Theo dõi tiến trình và cập nhật UI ít hơn
    int lastProgressUpdate = 0;
    int totalChunks = uploadState.uploadChunks?.length ?? 0;
    int completedChunks = 0;
    
    void updateProgress() {
      completedChunks++;
      // Chỉ cập nhật UI khi tiến trình thay đổi đáng kể (5%)
      int currentProgress = ((completedChunks / totalChunks) * 100).floor();
      if (currentProgress - lastProgressUpdate >= 5 || currentProgress == 100) {
        lastProgressUpdate = currentProgress;
        onProgress?.call(completedChunks / totalChunks);
      }
    }

    _listenConnectivityChanged(file, onError, objFile, uploadState);

    errorListener(dynamic error) =>
        _errorListener(uploadState.ref ?? '', error, onError, objFile);

    // Tạo StreamController để quản lý luồng chunk với back-pressure
    final chunkController = StreamController<UploadChunk>();
    int activeUploads = 0;
    final maxConcurrent = AppConfig.maxConcurrentChunks;
    
    // Message listener xử lý WebSocket responses
    late final Function(String) messageListener;

    messageListener = (String message) async {
      // Xử lý message nên được thực hiện trong một isolate hoặc compute để tránh blocking UI
      // Nhưng vì liên quan đến callback UI nên chúng ta sẽ sử dụng bất đồng bộ với microtask
      await Future.microtask(() {
        try {
          Map<String, dynamic> jsonResponse = jsonDecode(message);
          if (jsonResponse['ref'] != uploadState.ref) return;

          logDebug("Response for over 5mb file upload: $message");

          final response = FSFileChunkUploadResponse.fromJson(jsonResponse);
          final type = UploadType.fromString(response.type);

          switch (type) {
            case UploadType.initFileChunkUpload:
              _handleInitChunkUploadResponse(
                response, 
                uploadState, 
                onError, 
                objFile, 
                chunkController, 
                file, 
                uploadKey
              );
              break;

            case UploadType.fileChunkUpload:
              _handleChunkUploadResponse(
                response, 
                uploadState, 
                onError, 
                objFile, 
                file, 
                uploadKey,
                updateProgress
              );
              break;

            case UploadType.completeFileChunkUpload:
              _handleCompleteChunkUploadResponse(
                response, 
                uploadState, 
                onError, 
                objFile, 
                file, 
                uploadKey, 
                onSuccess,
                startTime,
                endTime
              );
              break;
            default:
              logDebug("Unknown response type: ${response.type}");
              break;
          }
        } catch (e) {
          logDebug("Error processing WebSocket message: $e");
        }
      });
    };

    // Xử lý luồng chunk với back-pressure
    chunkController.stream.listen((chunk) async {
      if (activeUploads >= maxConcurrent) return;
      
      activeUploads++;
      try {
        // Đọc chunk từ file khi cần, không giữ trong bộ nhớ
        final completer = Completer<void>();
        await _sendChunkAsync(
          file,
          chunk,
          uploadState,
          uploadKey,
          completer,
        );
        await completer.future;
      } finally {
        activeUploads--;
        
        // Kích hoạt gửi chunk tiếp theo nếu còn
        if (!chunkController.isClosed && uploadState.uploadChunks!.any((c) => !c.isCompleted)) {
          final nextChunk = uploadState.uploadChunks!.firstWhere(
            (c) => !c.isCompleted && !c.isProcessing,
            orElse: () => UploadChunk(offset: -1, chunkIndex: -1, isCompleted: true),
          );
          
          if (nextChunk.chunkIndex >= 0) {
            nextChunk.isProcessing = true;
            chunkController.add(nextChunk);
          }
        }
      }
    });

    _webSocketManager.addErrorListener(uploadKey, errorListener);
    _webSocketManager.sendMessage(
      uploadKey,
      uploadState.ref ?? '',
      initFileChunkUploadMeta.toJsonEncode(),
    );
    _webSocketManager.addMessageListener(uploadKey, messageListener);
  }

  void _handleInitChunkUploadResponse(
    FSFileChunkUploadResponse response, 
    UploadState uploadState, 
    onError, 
    objFile, 
    StreamController<UploadChunk> chunkController,
    File file,
    String uploadKey
  ) async {
    final fileChunkUploadData = FSFileChunkUploadData.fromJson(response.data);

    uploadState.uploadId = fileChunkUploadData.data?.uploadId;
    uploadState.s3UploadId = fileChunkUploadData.data?.s3UploadId;
    
    if (fileChunkUploadData.ok == false) {
      onError?.call(
        objFile,
        ErrorCode.uploadError,
        "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
      );
      chunkController.close();
    } else {
      // Bắt đầu tính thởi gian upload
      uploadState.startTime = DateTime.now();

      // Thêm các chunk ban đầu vào controller
      final initialChunks = uploadState.uploadChunks!
          .take(AppConfig.maxConcurrentChunks)
          .toList();
          
      for (final chunk in initialChunks) {
        chunk.isProcessing = true;
        chunkController.add(chunk);
      }
    }
  }

  void _handleChunkUploadResponse(
    FSFileChunkUploadResponse response, 
    UploadState uploadState, 
    onError, 
    objFile, 
    File file,
    String uploadKey,
    Function updateProgress
  ) {
    final fileChunkUploadData = FSFileChunkUploadDataV2.fromJson(response.data);

    if (fileChunkUploadData.ok == false) {
      _handleUploadError(
        file,
        objFile,
        uploadState,
        onError,
        ErrorCode.uploadError,
        "${fileChunkUploadData.error?.message}: ${fileChunkUploadData.error?.details}",
      );
    } else {
      // Cập nhật trạng thái của chunk
      final uploadChunk = fileChunkUploadData.data;
      if (uploadChunk != null && uploadChunk.isCompleted == true) {
        final chunkIndex = uploadChunk.chunkIndex;
        final chunk = uploadState.uploadChunks
            ?.where((uc) => uc.chunkIndex == uploadChunk.chunkIndex)
            .single;

        if (chunk != null) {
          chunk.isCompleted = true;
          chunk.chunkData = null; // Giải phóng bộ nhớ ngay lập tức
          chunk.isProcessing = false;
          uploadState.wsState?.currentChunks -= 1;
          
          // Cập nhật tiến trình
          updateProgress();
          
          // Tìm chunk tiếp theo để gửi
          final nextChunkToSend = uploadState.uploadChunks!.firstWhere(
            (c) => !c.isCompleted && !c.isProcessing,
            orElse: () => UploadChunk(offset: -1, chunkIndex: -1, isCompleted: true),
          );
          
          if (nextChunkToSend.chunkIndex >= 0) {
            nextChunkToSend.isProcessing = true;
            uploadState.currentChunkProcessing.add(nextChunkToSend);
          }
        }
      }

      // Kiểm tra nếu đã hoàn thành tất cả chunks
      final numChunkCompleted = uploadState.uploadChunks
          ?.where((uc) => uc.isCompleted == true)
          .length;

      if (numChunkCompleted == uploadState.uploadChunks?.length) {
        final completeFileChunkMeta = CompleteFileChunkMeta(
          type: UploadType.completeFileChunkUpload,
          uploadId: uploadState.uploadId!,
          s3UploadId: uploadState.s3UploadId!,
          ref: uploadState.ref,
        );

        _webSocketManager.sendMessage(
          uploadKey,
          uploadState.ref ?? '',
          completeFileChunkMeta.toJsonEncode(),
        );
      }
    }
  }

  void _handleCompleteChunkUploadResponse(
    FSFileChunkUploadResponse response, 
    UploadState uploadState, 
    onError, 
    objFile, 
    File file,
    String uploadKey,
    onSuccess,
    DateTime startTime,
    DateTime endTime
  ) {
    final completeFileChunkData = FSCompleteFileChunkData.fromJson(response.data);
    if (completeFileChunkData.ok == false) {
      _handleUploadError(
        file,
        objFile,
        uploadState,
        onError,
        ErrorCode.uploadError,
        "${completeFileChunkData.error?.message}: ${completeFileChunkData.error?.details}",
      );
    } else {
      final s3Url = completeFileChunkData.data?.s3Url;

      if (uploadState.cachedFilePath != null) {
        cacheManager.removeFileFromCacheList(uploadState.cachedFilePath!);
      }
      objFile.metaData = completeFileChunkData.data;
      onSuccess?.call(objFile, s3Url!);

      endTime = DateTime.now();
      uploadState.setUploadSpeed(startTime, endTime);
    }

    uploadState.wsState?.status == WebSocketStatus.idle;
    _webSocketManager.updateConnectionsActivityByCondition(
      uploadKey,
      WebSocketStatus.idle,
      ((state) => state.ref == uploadState.ref),
    );
  }

  // Phương thức đọc từng chunk trực tiếp từ file khi cần
  Future<Uint8List> _readChunk(File file, int offset, int size) async {
    final raf = await file.open();
    try {
      await raf.setPosition(offset);
      final chunk = await raf.read(size);
      return Uint8List.fromList(chunk);
    } finally {
      await raf.close();
    }
  }

  Future<void> _sendChunkAsync(
    File file,
    UploadChunk chunk,
    UploadState uploadState,
    String uploadKey,
    Completer<void> completer,
  ) async {
    try {
      // Tính MD5 trong isolate để không block UI thread
      chunk.md5ChunkHash = await compute(
        _calculateMd5ForChunk,
        {
          'filePath': file.path,
          'chunkIndex': chunk.chunkIndex,
          'chunkSize': uploadState.chunkSize
        },
      );

      // Đọc chunk từ file chỉ khi cần gửi
      final offset = chunk.chunkIndex * uploadState.chunkSize;
      final chunkData = await _readChunk(
        file, 
        offset, 
        uploadState.chunkSize
      );
      
      final fileMeta = FileChunkMeta(
        type: UploadType.fileChunkUpload,
        uploadId: uploadState.uploadId!,
        s3UploadId: uploadState.s3UploadId!,
        chunkIndex: chunk.chunkIndex,
        md5ChunkHash: chunk.md5ChunkHash,
        chunkData: chunkData,
        ref: uploadState.ref,
      );

      logDebug("Sending chunk ${chunk.chunkIndex}...");
      final wsState = uploadState.wsState;
      wsState?.currentChunks += 1;
      
      if (wsState != null &&
          wsState.currentChunks >= AppConfig.maxConcurrentChunks) {
        wsState.status = WebSocketStatus.chunkFull;
      }
      
      _webSocketManager.sendMessage(
        uploadKey,
        fileMeta.ref ?? '',
        fileMeta.toJsonEncode(),
      );
      
      // Giải phóng bộ nhớ sau khi gửi
      completer.complete();
    } catch (e) {
      logDebug("Error sending chunk ${chunk.chunkIndex}: $e");
      completer.completeError("Failed to send chunk ${chunk.chunkIndex}: $e");
    }
  }

  // Phương thức tính MD5 trong isolate
  static Future<String> _calculateMd5ForChunk(Map<String, dynamic> params) async {
    final filePath = params['filePath'] as String;
    final chunkIndex = params['chunkIndex'] as int;
    final chunkSize = params['chunkSize'] as int;
    
    final file = File(filePath);
    final offsetChunk = chunkIndex * chunkSize;
    
    return await AppConfig.calculateMd5ForChunkByIndex(
      file,
      chunkIndex,
      chunkSize,
    );
  }

  Future<void> _prepareFileChunksLite(
    File file,
    UploadState uploadState,
  ) async {
    int chunkSize = uploadState.chunkSize;
    int totalSize = await file.length();
    int currentOffset = 0;

    uploadState.uploadChunks = [];
    uploadState.currentChunkProcessing = [];

    while (currentOffset < totalSize) {
      final chunkIndex = currentOffset ~/ chunkSize;

      uploadState.uploadChunks?.add(
        UploadChunk(
          offset: currentOffset,
          chunkIndex: chunkIndex,
          md5ChunkHash: "",
          isCompleted: false,
          isProcessing: false,
          chunkData: null,
        ),
      );
      currentOffset += chunkSize;
    }

    logDebug(
      "Prepared ${uploadState.uploadChunks?.length ?? 0} chunks from file. Total size: $totalSize bytes.",
    );
  }
}