import 'dart:typed_data';

import '../data/models/api_response_classes/fs_file_data.dart';
import 'upload_state.dart';

class UpFile {
  final String path;
  final String name;
  int? size;
  FSFileData? metaData;
  UploadState? uploadState;
  Uint8List? fileData;
  String? key;
  String? ref;

  /// if key exited keep ws

  UpFile({
    required this.path,
    required this.name,
    this.size,
    this.metaData,
    this.uploadState,
    this.fileData,
    this.key,
    this.ref,
  });
}
