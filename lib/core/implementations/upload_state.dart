import 'package:flutter/foundation.dart';

import '../network/web_socket/web_socket_state.dart';
import '../utils/constants/config.dart';

class UploadState {
  int offset;
  int chunkIndex;
  int totalSize;
  int chunkSize;
  double? uploadSpeed;
  String? uploadId;
  String? s3UploadId;
  String? cachedFilePath;
  List<UploadChunk>? uploadChunks;
  List<UploadChunk> currentChunkProcessing = []; 
  List<Future<void>> uploadTasks = [];
  String? webSocketUrl;
  String? ref;
  WebSocketState? wsState;
  DateTime? startTime; 

  UploadState({
    required this.offset,
    required this.chunkIndex,
    required this.totalSize,
    required this.chunkSize,
    this.uploadSpeed,
    this.uploadId,
    this.s3UploadId,
    this.cachedFilePath,
    this.webSocketUrl,
    String? ref,
    this.wsState,
  }) {
    uploadChunks = [];
    this.ref = ref ?? AppConfig.generateUniqueId();
  }

  int get totalChunks => (totalSize / chunkSize).ceil();
  int get end =>
      (offset + chunkSize < totalSize) ? offset + chunkSize : totalSize;

  void setUploadSpeed(DateTime startTime, DateTime endTime) {
    final duration = endTime.difference(startTime).inSeconds;
    final dataSize = totalSize;
    uploadSpeed = (dataSize / duration) / 1024;

    if (kDebugMode) {
      print('Upload speed: $uploadSpeed KB/s. Duration: $duration(s)');
    }
  }

  @override
  String toString() {
    // TODO: implement toString
    String rs =
        "Offset: $offset, Chunkindex: $chunkIndex, TotalSize: $totalSize";
    return super.toString() + rs;
  }
}

class UploadChunk {
  int offset;
  int chunkIndex;
  String md5ChunkHash;
  bool isCompleted;
  bool isProcessing; 
  Uint8List? chunkData;

  UploadChunk({
    required this.offset,
    required this.chunkIndex,
    required this.md5ChunkHash,
    required this.isCompleted,
    this.isProcessing = false, 
    this.chunkData,
  });

  @override
  String toString() {
    // TODO: implement toString
    return "offset: $offset, chunkIndex: $chunkIndex, md5ChunkHash: $md5ChunkHash, isCompleted: $isCompleted, isProcessing: $isProcessing";
  }
}
