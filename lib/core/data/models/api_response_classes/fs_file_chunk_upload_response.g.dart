// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fs_file_chunk_upload_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FSFileChunkUploadResponse _$FSFileChunkUploadResponseFromJson(
        Map<String, dynamic> json) =>
    FSFileChunkUploadResponse(
      id: json['id'] as String,
      time: json['time'] as String,
      type: json['type'] as String,
      specversion: (json['specversion'] as num).toInt(),
      source: json['source'] as String,
      data: json['data'],
      ref: json['ref'] as String,
    );

Map<String, dynamic> _$FSFileChunkUploadResponseToJson(
        FSFileChunkUploadResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'time': instance.time,
      'type': instance.type,
      'specversion': instance.specversion,
      'source': instance.source,
      'data': instance.data,
      'ref': instance.ref,
    };

FSFileChunkUploadData _$FSFileChunkUploadDataFromJson(
        Map<String, dynamic> json) =>
    FSFileChunkUploadData(
      ok: json['ok'] as bool,
      error: json['error'] == null
          ? null
          : FSFileError.fromJson(json['error'] as Map<String, dynamic>),
      data: json['data'] == null
          ? null
          : FSFileChunkUploadDetailData.fromJson(
              json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSFileChunkUploadDataToJson(
        FSFileChunkUploadData instance) =>
    <String, dynamic>{
      'ok': instance.ok,
      'error': instance.error?.toJson(),
      'data': instance.data?.toJson(),
    };

FSFileChunkUploadDataV2 _$FSFileChunkUploadDataV2FromJson(
        Map<String, dynamic> json) =>
    FSFileChunkUploadDataV2(
      ok: json['ok'] as bool,
      error: json['error'] == null
          ? null
          : FSFileError.fromJson(json['error'] as Map<String, dynamic>),
      data: json['data'] == null
          ? null
          : FSUploadChunk.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSFileChunkUploadDataV2ToJson(
        FSFileChunkUploadDataV2 instance) =>
    <String, dynamic>{
      'ok': instance.ok,
      'error': instance.error?.toJson(),
      'data': instance.data?.toJson(),
    };

FSCompleteFileChunkData _$FSCompleteFileChunkDataFromJson(
        Map<String, dynamic> json) =>
    FSCompleteFileChunkData(
      ok: json['ok'] as bool,
      error: json['error'] == null
          ? null
          : FSFileError.fromJson(json['error'] as Map<String, dynamic>),
      data: json['data'] == null
          ? null
          : FSFileData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSCompleteFileChunkDataToJson(
        FSCompleteFileChunkData instance) =>
    <String, dynamic>{
      'ok': instance.ok,
      'error': instance.error?.toJson(),
      'data': instance.data?.toJson(),
    };

FSFileChunkUploadDetailData _$FSFileChunkUploadDetailDataFromJson(
        Map<String, dynamic> json) =>
    FSFileChunkUploadDetailData(
      userId: json['userId'] as String?,
      md5FileHash: json['md5FileHash'] as String?,
      totalChunks: (json['totalChunks'] as num?)?.toInt(),
      name: json['name'] as String?,
      type: json['type'] as String,
      uploadId: json['uploadId'] as String,
      s3UploadId: json['s3UploadId'] as String,
      uploadChunks: (json['uploadChunks'] as List<dynamic>)
          .map((e) => FSUploadChunk.fromJson(e as Map<String, dynamic>))
          .toList(),
      uploadChunk: json['uploadChunk'] == null
          ? null
          : FSUploadChunk.fromJson(json['uploadChunk'] as Map<String, dynamic>),
      fullPath: json['fullPath'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      fileInfo: json['fileInfo'] == null
          ? null
          : FSFileInfo.fromJson(json['fileInfo'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSFileChunkUploadDetailDataToJson(
        FSFileChunkUploadDetailData instance) =>
    <String, dynamic>{
      'userId': instance.userId,
      'md5FileHash': instance.md5FileHash,
      'totalChunks': instance.totalChunks,
      'name': instance.name,
      'type': instance.type,
      'uploadId': instance.uploadId,
      's3UploadId': instance.s3UploadId,
      'uploadChunks': instance.uploadChunks,
      'uploadChunk': instance.uploadChunk,
      'fullPath': instance.fullPath,
      'fileSize': instance.fileSize,
      'fileInfo': instance.fileInfo,
    };

FSUploadChunk _$FSUploadChunkFromJson(Map<String, dynamic> json) =>
    FSUploadChunk(
      chunkIndex: (json['chunkIndex'] as num).toInt(),
      partNumber: (json['PartNumber'] as num).toInt(),
      eTag: json['ETag'] as String,
      isCompleted: json['isCompleted'] as bool,
      md5ChunkHash: json['md5ChunkHash'] as String,
      ref: json['ref'] as String,
    );

Map<String, dynamic> _$FSUploadChunkToJson(FSUploadChunk instance) =>
    <String, dynamic>{
      'chunkIndex': instance.chunkIndex,
      'PartNumber': instance.partNumber,
      'ETag': instance.eTag,
      'isCompleted': instance.isCompleted,
      'md5ChunkHash': instance.md5ChunkHash,
      'ref': instance.ref,
    };

FSFileInfo _$FSFileInfoFromJson(Map<String, dynamic> json) => FSFileInfo(
      ext: json['ext'] as String,
      mime: json['mime'] as String,
    );

Map<String, dynamic> _$FSFileInfoToJson(FSFileInfo instance) =>
    <String, dynamic>{
      'ext': instance.ext,
      'mime': instance.mime,
    };
