import 'package:json_annotation/json_annotation.dart';

import 'fs_file_data.dart';
import 'fs_file_error.dart';

part 'fs_file_chunk_upload_response.g.dart';

@JsonSerializable(explicitToJson: true)
class FSFileChunkUploadResponse {
  final String id;
  final String time;
  final String type;
  final int specversion;
  final String source;
  final dynamic data;
  final String ref;

  FSFileChunkUploadResponse({
    required this.id,
    required this.time,
    required this.type,
    required this.specversion,
    required this.source,
    required this.data,
    required this.ref,
  });

  factory FSFileChunkUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FSFileChunkUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileChunkUploadResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FSFileChunkUploadData {
  final bool ok;
  final FSFileError? error;
  final FSFileChunkUploadDetailData? data;

  FSFileChunkUploadData({
    required this.ok,
    this.error,
    this.data,
  });

  factory FSFileChunkUploadData.fromJson(Map<String, dynamic> json) =>
      _$FSFileChunkUploadDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileChunkUploadDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FSFileChunkUploadDataV2 {
  final bool ok;
  final FSFileError? error;
  final FSUploadChunk? data;

  FSFileChunkUploadDataV2({
    required this.ok,
    this.error,
    this.data,
  });

  // Sử dụng đúng hàm do json_serializable tạo
  factory FSFileChunkUploadDataV2.fromJson(Map<String, dynamic> json) =>
      _$FSFileChunkUploadDataV2FromJson(json);

  Map<String, dynamic> toJson() => _$FSFileChunkUploadDataV2ToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FSCompleteFileChunkData {
  final bool ok;
  final FSFileError? error;
  final FSFileData? data;

  FSCompleteFileChunkData({
    required this.ok,
    this.error,
    this.data,
  });

  factory FSCompleteFileChunkData.fromJson(Map<String, dynamic> json) =>
      _$FSCompleteFileChunkDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSCompleteFileChunkDataToJson(this);
}

@JsonSerializable()
class FSFileChunkUploadDetailData {
  final String? userId;
  final String? md5FileHash;
  final int? totalChunks;
  final String? name;
  final String type;
  final String uploadId;
  final String s3UploadId;
  final List<FSUploadChunk> uploadChunks;
  final FSUploadChunk? uploadChunk;
  final String fullPath;
  final int fileSize;
  final FSFileInfo? fileInfo; // Thêm trường fileInfo

  FSFileChunkUploadDetailData({
    this.userId,
    this.md5FileHash,
    this.totalChunks,
    this.name,
    required this.type,
    required this.uploadId,
    required this.s3UploadId,
    required this.uploadChunks,
    this.uploadChunk,
    required this.fullPath,
    required this.fileSize,
    this.fileInfo,
  });

  factory FSFileChunkUploadDetailData.fromJson(Map<String, dynamic> json) =>
      _$FSFileChunkUploadDetailDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileChunkUploadDetailDataToJson(this);
}

@JsonSerializable()
class FSUploadChunk {
  final int chunkIndex;
  @JsonKey(name: 'PartNumber')
  final int partNumber;
  @JsonKey(name: 'ETag')
  final String eTag;
  final bool isCompleted;
  final String md5ChunkHash;
  final String ref;

  FSUploadChunk({
    required this.chunkIndex,
    required this.partNumber,
    required this.eTag,
    required this.isCompleted,
    required this.md5ChunkHash,
    required this.ref,
  });

  factory FSUploadChunk.fromJson(Map<String, dynamic> json) =>
      _$FSUploadChunkFromJson(json);

  Map<String, dynamic> toJson() => _$FSUploadChunkToJson(this);
}

@JsonSerializable()
class FSFileInfo {
  final String ext;
  final String mime;

  FSFileInfo({
    required this.ext,
    required this.mime,
  });

  factory FSFileInfo.fromJson(Map<String, dynamic> json) =>
      _$FSFileInfoFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileInfoToJson(this);
}
