// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fs_file_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FSFileData _$FSFileDataFromJson(Map<String, dynamic> json) => FSFileData(
      parentId: json['parentId'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      type: json['type'] as String,
      md5FileHash: json['md5FileHash'] as String,
      fileType: json['fileType'] as String,
      fileSize: (json['fileSize'] as num).toInt(),
      fullPath: json['fullPath'] as String,
      s3FilePath: json['s3FilePath'] as String,
      createTime: json['createTime'] as String,
      id: json['_id'] as String,
      uploadId: json['uploadId'] as String?,
      s3Url: json['s3Url'] as String?,
    );

Map<String, dynamic> _$FSFileDataToJson(FSFileData instance) =>
    <String, dynamic>{
      'parentId': instance.parentId,
      'userId': instance.userId,
      'name': instance.name,
      'type': instance.type,
      'md5FileHash': instance.md5FileHash,
      'fileType': instance.fileType,
      'fileSize': instance.fileSize,
      'fullPath': instance.fullPath,
      's3FilePath': instance.s3FilePath,
      'createTime': instance.createTime,
      '_id': instance.id,
      'uploadId': instance.uploadId,
      's3Url': instance.s3Url,
    };
