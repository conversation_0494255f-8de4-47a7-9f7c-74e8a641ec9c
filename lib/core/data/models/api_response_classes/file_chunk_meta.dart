import 'dart:convert';
import 'dart:typed_data';

import 'package:json_annotation/json_annotation.dart';

import '../../../utils/constants/enums.dart';
import 'fs_unit8_list_converter.dart';

@JsonSerializable(explicitToJson: true)
class FileChunkMeta {
  final UploadType type;
  final String uploadId;
  final String s3UploadId;
  final int chunkIndex;
  final String md5ChunkHash;
  @Uint8ListConverter()
  final Uint8List chunkData;
  final String ref;

  FileChunkMeta({
    required this.type,
    required this.uploadId,
    required this.s3UploadId,
    required this.chunkIndex,
    required this.md5ChunkHash,
    required this.chunkData,
    String? ref,
  }) : ref = ref ?? '';

  String toJsonEncode() {
    return json.encode({
      'ref': ref,
      'type': type.value,
      'uploadId': uploadId,
      's3UploadId': s3UploadId,
      'chunkIndex': chunkIndex,
      'md5ChunkHash': md5ChunkHash,
      'chunkData': chunkData,
    });
  }
}
