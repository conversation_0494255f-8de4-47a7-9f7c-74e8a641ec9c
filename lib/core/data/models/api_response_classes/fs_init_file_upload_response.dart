import 'package:json_annotation/json_annotation.dart';

import 'fs_file_data.dart';
import 'fs_file_error.dart';

part 'fs_init_file_upload_response.g.dart';

@JsonSerializable(explicitToJson: true)
class FSInitFileUploadResponse {
  final String id;
  final String time;
  final String type;
  final int specversion;
  final String source;
  final FSInitFileUploadData data;
  final String ref;

  FSInitFileUploadResponse({
    required this.id,
    required this.time,
    required this.type,
    required this.specversion,
    required this.source,
    required this.data,
    required this.ref,
  });

  factory FSInitFileUploadResponse.fromJson(Map<String, dynamic> json) =>
      _$FSInitFileUploadResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FSInitFileUploadResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FSInitFileUploadData {
  final bool ok;
  final FSFileData? data;
  final FSFileError? error;

  FSInitFileUploadData({
    required this.ok,
    this.data,
    this.error,
  });

  factory FSInitFileUploadData.fromJson(Map<String, dynamic> json) =>
      _$FSInitFileUploadDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSInitFileUploadDataToJson(this);
}
