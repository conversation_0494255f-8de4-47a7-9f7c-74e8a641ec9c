import 'package:json_annotation/json_annotation.dart';

part 'fs_file_error.g.dart';

@JsonSerializable()
class FSFileError {
  final int code;
  final String message;
  final List<String> details;

  FSFileError({
    required this.code,
    required this.message,
    required this.details,
  });

  factory FSFileError.fromJson(Map<String, dynamic> json) =>
      _$FSFileErrorFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileErrorToJson(this);
}
