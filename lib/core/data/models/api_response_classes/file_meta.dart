import 'dart:convert';
import 'dart:typed_data';

import 'package:json_annotation/json_annotation.dart';

import '../../../utils/constants/config.dart';
import '../../../utils/constants/enums.dart';
import 'fs_unit8_list_converter.dart';

@JsonSerializable(explicitToJson: true)
class FileMeta {
  final String ref;
  final UploadType type;
  final String name;
  final String md5FileHash;
  @Uint8ListConverter()
  final Uint8List fileData;

  FileMeta({
    String? ref,
    required this.type,
    required this.name,
    required this.md5FileHash,
    required this.fileData,
  }) : ref = ref ?? AppConfig.generateUniqueId();

  String toJsonEncode() {
    return json.encode({
      'ref': ref,
      'type': type.value,
      'name': name,
      'md5FileHash': md5FileHash,
      'fileData': fileData,
    });
  }
}
