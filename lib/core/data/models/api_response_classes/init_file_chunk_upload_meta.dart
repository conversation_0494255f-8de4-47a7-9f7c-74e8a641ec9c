import 'dart:convert';

import '../../../utils/constants/config.dart';
import '../../../utils/constants/enums.dart';

class InitFileChunkUploadMeta {
  final UploadType type;
  final String name;
  final String? md5FileHash;
  final int totalChunks;
  final int fileSize;
  final String ref;

  InitFileChunkUploadMeta({
    required this.type,
    required this.name,
    this.md5FileHash,
    required this.totalChunks,
    required this.fileSize,
    String? ref,
  }) : ref = ref ?? AppConfig.generateUniqueId();

  String toJsonEncode() {
    final Map<String, dynamic> jsonMap = {
      'type': type.value,
      'name': name,
      'totalChunks': totalChunks,
      'fileSize': fileSize,
      'ref': ref,
    };

    if (md5FileHash != null) {
      jsonMap['md5FileHash'] = md5FileHash;
    }

    return json.encode(jsonMap);
  }
}
