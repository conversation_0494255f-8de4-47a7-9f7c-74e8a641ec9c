// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fs_init_file_upload_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FSInitFileUploadResponse _$FSInitFileUploadResponseFromJson(
        Map<String, dynamic> json) =>
    FSInitFileUploadResponse(
      id: json['id'] as String,
      time: json['time'] as String,
      type: json['type'] as String,
      specversion: (json['specversion'] as num).toInt(),
      source: json['source'] as String,
      data: FSInitFileUploadData.fromJson(json['data'] as Map<String, dynamic>),
      ref: json['ref'] as String,
    );

Map<String, dynamic> _$FSInitFileUploadResponseToJson(
        FSInitFileUploadResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'time': instance.time,
      'type': instance.type,
      'specversion': instance.specversion,
      'source': instance.source,
      'data': instance.data.toJson(),
      'ref': instance.ref,
    };

FSInitFileUploadData _$FSInitFileUploadDataFromJson(
        Map<String, dynamic> json) =>
    FSInitFileUploadData(
      ok: json['ok'] as bool,
      data: json['data'] == null
          ? null
          : FSFileData.fromJson(json['data'] as Map<String, dynamic>),
      error: json['error'] == null
          ? null
          : FSFileError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSInitFileUploadDataToJson(
        FSInitFileUploadData instance) =>
    <String, dynamic>{
      'ok': instance.ok,
      'data': instance.data?.toJson(),
      'error': instance.error?.toJson(),
    };
