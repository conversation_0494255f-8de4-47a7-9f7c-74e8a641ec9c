import 'package:json_annotation/json_annotation.dart';

part 'fs_base_response.g.dart';

@JsonSerializable(genericArgumentFactories: true, explicitToJson: true)
class FSBaseResponse<T> {
  final bool ok;
  final T? data;
  final FSError? error;

  FSBaseResponse({required this.ok, this.data, this.error});

  // Factory constructor for deserialization
  factory FSBaseResponse.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$FSBaseResponseFromJson(json, fromJsonT);

  // Method for serialization
  Map<String, dynamic> toJson(Object Function(T value) toJsonT) =>
      _$FSBaseResponseToJson(this, toJsonT);
}

@JsonSerializable()
class FSError {
  final int? code;
  final String? message;
  final List<String>? details;

  FSError({this.code, this.message, this.details});

  factory FSError.fromJson(Map<String, dynamic> json) =>
      _$FSErrorFromJson(json);

  Map<String, dynamic> toJson() => _$FSErrorToJson(this);
}

@JsonSerializable()
class FSData {
  final String url;

  FSData({required this.url});

  factory FSData.fromJson(Map<String, dynamic> json) => _$FSDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSDataToJson(this);
}
