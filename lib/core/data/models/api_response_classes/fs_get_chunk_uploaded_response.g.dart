// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fs_get_chunk_uploaded_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FsGetChunkUploadedResponse _$FsGetChunkUploadedResponseFromJson(
        Map<String, dynamic> json) =>
    FsGetChunkUploadedResponse(
      id: json['id'] as String,
      time: json['time'] as String,
      type: json['type'] as String,
      specversion: (json['specversion'] as num).toInt(),
      source: json['source'] as String,
      data:
          FSGetChunkUploadedData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FsGetChunkUploadedResponseToJson(
        FsGetChunkUploadedResponse instance) =>
    <String, dynamic>{
      'id': instance.id,
      'time': instance.time,
      'type': instance.type,
      'specversion': instance.specversion,
      'source': instance.source,
      'data': instance.data.toJson(),
    };

FSGetChunkUploadedData _$FSGetChunkUploadedDataFromJson(
        Map<String, dynamic> json) =>
    FSGetChunkUploadedData(
      ok: json['ok'] as bool,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => FSUploadChunk.fromJson(e as Map<String, dynamic>))
          .toList(),
      error: json['error'] == null
          ? null
          : FSFileError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSGetChunkUploadedDataToJson(
        FSGetChunkUploadedData instance) =>
    <String, dynamic>{
      'ok': instance.ok,
      'data': instance.data?.map((e) => e.toJson()).toList(),
      'error': instance.error?.toJson(),
    };
