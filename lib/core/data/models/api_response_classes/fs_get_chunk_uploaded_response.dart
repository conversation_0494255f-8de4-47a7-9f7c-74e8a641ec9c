import 'package:json_annotation/json_annotation.dart';

import 'fs_file_chunk_upload_response.dart';
import 'fs_file_error.dart';

part 'fs_get_chunk_uploaded_response.g.dart';

@JsonSerializable(explicitToJson: true)
class FsGetChunkUploadedResponse {
  final String id;
  final String time;
  final String type;
  final int specversion;
  final String source;
  final FSGetChunkUploadedData
      data; // Dữ liệu có thể là các kiểu khác nhau, nên sử dụng dynamic

  FsGetChunkUploadedResponse({
    required this.id,
    required this.time,
    required this.type,
    required this.specversion,
    required this.source,
    required this.data,
  });

  factory FsGetChunkUploadedResponse.fromJson(Map<String, dynamic> json) =>
      _$FsGetChunkUploadedResponseFromJson(json);

  Map<String, dynamic> toJson() => _$FsGetChunkUploadedResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class FSGetChunkUploadedData {
  final bool ok;
  final List<FSUploadChunk>? data;
  final FSFileError? error;

  FSGetChunkUploadedData({
    required this.ok,
    this.data,
    this.error,
  });

  factory FSGetChunkUploadedData.fromJson(Map<String, dynamic> json) =>
      _$FSGetChunkUploadedDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSGetChunkUploadedDataToJson(this);
}
