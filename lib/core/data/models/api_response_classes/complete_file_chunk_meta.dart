import 'dart:convert';

import '../../../utils/constants/enums.dart';

class CompleteFileChunkMeta {
  final UploadType type;
  final String uploadId;
  final String s3UploadId;
  final String ref;

  CompleteFileChunkMeta({
    required this.type,
    required this.uploadId,
    required this.s3UploadId,
    String? ref,
  }) : ref = ref ?? '';

  String toJsonEncode() {
    return json.encode({
      'ref': ref,
      'type': type.value,
      'uploadId': uploadId,
      's3UploadId': s3UploadId,
    });
  }
}
