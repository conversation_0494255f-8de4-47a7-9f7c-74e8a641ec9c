// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fs_base_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FSBaseResponse<T> _$FSBaseResponseFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    FSBaseResponse<T>(
      ok: json['ok'] as bool,
      data: _$nullableGenericFromJson(json['data'], fromJsonT),
      error: json['error'] == null
          ? null
          : FSError.fromJson(json['error'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$FSBaseResponseToJson<T>(
  FSBaseResponse<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'ok': instance.ok,
      'data': _$nullableGenericToJson(instance.data, toJsonT),
      'error': instance.error?.toJson(),
    };

T? _$nullableGenericFromJson<T>(
  Object? input,
  T Function(Object? json) fromJson,
) =>
    input == null ? null : fromJson(input);

Object? _$nullableGenericToJson<T>(
  T? input,
  Object? Function(T value) toJson,
) =>
    input == null ? null : toJson(input);

FSError _$FSErrorFromJson(Map<String, dynamic> json) => FSError(
      code: (json['code'] as num?)?.toInt(),
      message: json['message'] as String?,
      details:
          (json['details'] as List<dynamic>?)?.map((e) => e as String).toList(),
    );

Map<String, dynamic> _$FSErrorToJson(FSError instance) => <String, dynamic>{
      'code': instance.code,
      'message': instance.message,
      'details': instance.details,
    };

FSData _$FSDataFromJson(Map<String, dynamic> json) => FSData(
      url: json['url'] as String,
    );

Map<String, dynamic> _$FSDataToJson(FSData instance) => <String, dynamic>{
      'url': instance.url,
    };
