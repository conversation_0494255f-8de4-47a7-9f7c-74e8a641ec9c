import 'package:json_annotation/json_annotation.dart';
part 'fs_file_data.g.dart';

@JsonSerializable()
class FSFileData {
  final String parentId;
  final String userId;
  final String name;
  final String type;
  final String md5FileHash;
  final String fileType;
  final int fileSize;
  final String fullPath;
  final String s3FilePath;
  final String createTime;
  @JsonKey(name: '_id')
  final String id;
  final String? uploadId;
  final String? s3Url;

  FSFileData({
    required this.parentId,
    required this.userId,
    required this.name,
    required this.type,
    required this.md5FileHash,
    required this.fileType,
    required this.fileSize,
    required this.fullPath,
    required this.s3FilePath,
    required this.createTime,
    required this.id,
    this.uploadId,
    this.s3Url,
  });

  factory FSFileData.fromJson(Map<String, dynamic> json) =>
      _$FSFileDataFromJson(json);

  Map<String, dynamic> toJson() => _$FSFileDataToJson(this);
}
