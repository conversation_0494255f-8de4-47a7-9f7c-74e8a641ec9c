import 'dart:convert';

import '../../../utils/constants/enums.dart';

class CompleteFileMeta {
  final UploadType type;
  final String uploadId;
  final String ref;

  CompleteFileMeta({
    required this.type,
    required this.uploadId,
    String? ref,
  }) : ref = ref ?? '';

  String toJsonEncode() {
    return json.encode({
      'type': type.value,
      'uploadId': uploadId,
      'ref': ref,
    });
  }
}
