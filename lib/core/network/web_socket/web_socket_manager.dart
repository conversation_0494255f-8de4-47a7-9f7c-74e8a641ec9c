import 'dart:async';
import 'dart:convert';
import 'package:collection/collection.dart';
import 'package:web_socket_channel/io.dart';

import '../../utils/constants/config.dart';
import '../../utils/exceptions/file_store_exception.dart';
import 'web_socket_state.dart';
import 'web_socket_task.dart';

class WebSocketManager {
  static final WebSocketManager _instance = WebSocketManager._internal();

  factory WebSocketManager() {
    return _instance;
  }

  /// Internal constructor for Singleton
  WebSocketManager._internal();

  final Map<String, List<WebSocketState>> _activeWebsockets = {};
  final PriorityQueue<WebSocketTask> _taskQueue = PriorityQueue();
  final int maxActiveSockets = 3;
  int _processingTasks = 0;

  void debug(String message) => AppConfig.printDebugMode(message);

  final Map<String, List<Function(String)>> _messageListenersByKey = {};

  final Map<String, List<Function(dynamic error)>> _errorListenersByKey = {};

  /// Register a new error listener
  void addErrorListener(String key, Function(dynamic error) callback) {
    debug("WebSocket Add Error Listener for key [$key]");
    if (!_errorListenersByKey.containsKey(key)) {
      _errorListenersByKey[key] = [];
    }
    _errorListenersByKey[key]!.add(callback);
  }

  /// Remove an existing error listener
  void removeErrorListener(String key, Function(dynamic error) callback) {
    debug("WebSocket Remove Error Listener for key [$key]");
    final listeners = _errorListenersByKey[key];
    if (listeners != null) {
      listeners.remove(callback);
      if (listeners.isEmpty) {
        _errorListenersByKey.remove(key);
      }
    }
  }

  /// Notify all registered listeners for a specific WebSocket `key` about an error
  void _notifyErrorListeners(
    IOWebSocketChannel channel,
    String key,
    dynamic error,
  ) {
    debug(
      "Notifying error listeners for WebSocket [$key] with error: ${error.toString()}",
    );

    final listeners = _errorListenersByKey[key];
    if (listeners != null) {
      final ref = _getFileRefState(key, channel);
      for (final callback in listeners) {
        callback({"ref": ref, "error": error});
      }
    }
  }

  void _updateActivity(String key) {
    final states = _activeWebsockets[key];
    if (states != null && states.isNotEmpty) {
      for (final state in states) {
        state.lastActive = DateTime.now();
        if (state.status != WebSocketStatus.chunkFull) {
          state.status = WebSocketStatus.active;
        }
      }
    }
  }

  void updateConnectionsActivityByCondition(
    String key,
    WebSocketStatus status,
    bool Function(WebSocketState) predicate,
  ) {
    final states = _activeWebsockets[key];
    if (states != null) {
      final targetState = states.firstWhereOrNull(predicate);
      if (targetState != null) {
        targetState.status = status;

        debug(
          "Update connections state [${targetState.toString()}] to: $status for specific state.",
        );

        if (status == WebSocketStatus.idle) {
          _processQueue();
          debugTrackingWSState();
        }
      } else {
        debug("No WebSocketState matches the condition for key [$key].");
      }
    } else {
      debug("No active WebSocketStates found for key [$key].");
    }
  }

  void autoReleaseInactiveConnections(Duration timeout) {
    final now = DateTime.now();

    _activeWebsockets.forEach((key, states) {
      final idleStates = states.where(
        (state) =>
            state.status == WebSocketStatus.idle &&
            now.difference(state.lastActive) > timeout,
      );

      for (final state in idleStates) {
        states.remove(state);
        state.channel.sink.close();
        debug("Released WebSocket for key [$key]");
      }
    });
  }

  void releaseWebSocket(String key, WebSocketState targetState) {
    final states = _activeWebsockets[key];
    if (states != null) {
      final removed = states.remove(targetState);
      if (removed) {
        debug(
          "Released WebSocket for key [$key] and target state. Ref: ${targetState.ref}",
        );
        debugTrackingWSState();
      } else {
        debug("Target WebSocketState not found for key [$key].");
      }

      if (states.isEmpty) {
        _activeWebsockets.remove(key);
      }

      //TODO remove error listener
    } else {
      debug("No active WebSocketStates found for key [$key].");
    }
    if (_taskQueue.isNotEmpty) _processQueue();
  }

  Future<void> _processQueue() async {
    // Đếm số lượng WebSocket đang hoạt động và đang được xử lý
    final activeSocketCount = _activeWebsockets.values.fold<int>(
          0,
          (sum, states) =>
              sum +
              states
                  .where((state) => state.status != WebSocketStatus.idle)
                  .length,
        ) +
        _processingTasks;

    if (activeSocketCount >= maxActiveSockets) {
      return;
    }

    if (_taskQueue.isNotEmpty) {
      _processingTasks++;

      final WebSocketTask task = _taskQueue.removeFirst();
      await _connectWebSocket(
        task.key,
        task.ref,
        task.getUrlCallback,
        headers: task.headers,
      ).then((state) {
        task.url = state.url;
        // Thêm WebSocketState vào danh sách liên quan đến `key`
        final states = _activeWebsockets[task.key] ?? [];
        debug("states.where((state) => ${state.ref} == ${task.ref}).isEmpty");
        if (states.where((state) => state.ref == task.ref).isEmpty) {
          states.add(state);
        }

        _activeWebsockets[task.key] = states;

        task.onComplete?.call(); // Gọi callback khi kết nối thành công
        task.completer.complete(state);
      }).catchError((error) {
        // Lấy targetState từ task hoặc trạng thái tạm thời
        final targetStates = _activeWebsockets[task.key];
        final targetState = (targetStates != null && targetStates.isNotEmpty)
            ? targetStates.last
            : null;

        if (targetState != null) {
          // Xử lý lỗi cho trạng thái cụ thể
          _handleWebSocketError(task.key, error, task, targetState);
        } else {
          debug(
            "No valid targetState found for key [${task.key}] during error handling.",
          );
        }
        task.completer.completeError(error);
        throw error;
      }).whenComplete(() {
        _processingTasks--;
        _processQueue();
      });
    }
  }

  void startAutoRelease(Duration interval, Duration timeout) {
    Timer.periodic(interval, (timer) {
      autoReleaseInactiveConnections(timeout);
    });
  }

  Future<void> retryWebSocket(
    String key,
    WebSocketTask task,
    int retryCount,
  ) async {
    for (int i = 0; i < retryCount; i++) {
      try {
        final state = await _connectWebSocket(
          task.key,
          task.ref,
          task.getUrlCallback,
          headers: task.headers,
        );
        debug("WebSocket [$key] reconnected successfully.");

        // Thêm state vào danh sách cho key
        final states = _activeWebsockets[key] ?? [];
        states.add(state);
        _activeWebsockets[key] = states;

        task.onComplete?.call();
        break;
      } catch (e) {
        debug("Retry (${i + 1}) failed for WebSocket [$key]: $e");
        await Future.delayed(const Duration(seconds: 3));
      }
    }
  }

  void _handleWebSocketError(
    String key,
    dynamic error,
    WebSocketTask task,
    WebSocketState targetState,
  ) {
    debug("Error on WebSocket [$key]: $error");

    // Kiểm tra sự tồn tại của targetState trong danh sách _activeWebsockets
    if (_activeWebsockets.containsKey(key)) {
      final states = _activeWebsockets[key];
      if (states != null && states.contains(targetState)) {
        targetState.status = WebSocketStatus.error;
        debug("Updated status to error for target state in key [$key].");
      } else {
        debug("Target state not found for key [$key].");
      }
    } else {
      debug("No active WebSocketStates found for key [$key].");
    }

    // Retry với tối đa 3 lần
    // retryWebSocket(key, task, 3);
  }

  Future<WebSocketState?> enqueueWebSocket(
    String key,
    String ref,
    Future<String> Function() getUrlCallback, {
    Map<String, dynamic>? headers,
  }) async {
    final task = WebSocketTask(
      key: key,
      ref: ref,
      getUrlCallback: getUrlCallback,
      headers: headers,
      onComplete: () =>
          debug("WebSocket [$key] connected successfully. Ref: [$ref]"),
    );

    _taskQueue.add(task);
    _processQueue();
    return task.completer.future;
  }

  Future<WebSocketState> _connectWebSocket(
    String key,
    String ref,
    Future<String> Function() getUrlCallback, {
    Map<String, dynamic>? headers,
  }) async {
    debug("Connecting WebSocket with key [$key][$ref]");

    _activeWebsockets.putIfAbsent(key, () => []);

    // Kiểm tra xem có WebSocket nào đang idle hay không
    final idleConnection = reuseIdleConnection(key, ref);
    if (idleConnection != null) {
      debug("idleConnection for key [$key][${idleConnection.ref}]");
      return idleConnection;
    }

    // Kiểm tra số lượng kết nối hiện tại so với maxActiveSockets
    final activeSocketCount =
        _activeWebsockets.values.expand((states) => states).length;

    if (activeSocketCount >= maxActiveSockets) {
      final idleSockets = _findAllIdleWebSockets();
      debug(
        "Giải phóng WebSocket nhàn rỗi > 45s . _findIdleWebSocket: ${idleSockets.toString()}",
      );
      if (idleSockets.isNotEmpty) {
        for (final ws in idleSockets) {
          releaseWebSocket(ws.key, ws.value);
        }
      } else {
        debug("All WebSockets are busy. Please wait.");
        throw Exception('All WebSockets are busy. Please wait.');
      }
    }

    try {
      final String url = await getUrlCallback();
      final channel = IOWebSocketChannel.connect(url, headers: headers);
      if (channel.closeCode == null) {
        debug("New WebSocket for key [$key][$ref]");
        final state = WebSocketState(
          channel: channel,
          lastActive: DateTime.now(),
          status: WebSocketStatus.active,
          ref: ref,
          url: url,
        );

        _listenToChannel(channel, key, ref);
        return state;
      } else {
        throw FileStoreException(
          "Can't connect websocket. Error: ${channel.closeReason}",
        );
      }
    } catch (error) {
      debug("Failed to connect WebSocket [$key]: $error");
      rethrow;
    }
  }

  MapEntry<String, WebSocketState>? _findIdleWebSocket(String key) {
    for (final entry in _activeWebsockets.entries) {
      for (final state in entry.value) {
        if (state.status == WebSocketStatus.idle) {
          return MapEntry(entry.key, state); // Trả về MapEntry đúng
        }
      }
    }
    return null;
  }

  List<MapEntry<String, WebSocketState>> _findAllIdleWebSockets() {
    final List<MapEntry<String, WebSocketState>> idleSockets = [];

    for (final entry in _activeWebsockets.entries) {
      for (final state in entry.value) {
        if (state.status == WebSocketStatus.idle &&
            DateTime.now().difference(state.lastActive).inMilliseconds >=
                AppConfig.timeoutListenWebsocketDuration.inMilliseconds) {
          idleSockets.add(MapEntry(entry.key, state));
        }
      }
    }
    return idleSockets; // Trả về danh sách tất cả WebSocket idle
  }

  /// Open WebSocket connection
  Future<WebSocketState?> connect(
    String key,
    String ref,
    Future<String> Function() getUrlCallback, {
    Map<String, dynamic>? headers,
  }) async {
    final state =
        await enqueueWebSocket(key, ref, getUrlCallback, headers: headers);

    debug("Tracking add state to key connect: ${_activeWebsockets.toString()}");
    return state;
  }

  WebSocketState? reuseIdleConnection(String key, String ref) {
    final existingWebSocket = _activeWebsockets[key]?.firstWhereOrNull(
      (ws) =>
          ws.status == WebSocketStatus.idle &&
          DateTime.now().difference(ws.lastActive).inMilliseconds <
              AppConfig.timeoutListenWebsocketDuration.inMilliseconds,
    );

    if (existingWebSocket != null) {
      debug("existingWebSocket.ref: ${existingWebSocket.ref} ");

      existingWebSocket.status = WebSocketStatus.active;
      existingWebSocket.lastActive = DateTime.now();
      existingWebSocket.ref = ref;
      return existingWebSocket;
    }

    return null;
  }

  String _getFileRefState(String key, IOWebSocketChannel channel) {
    return _activeWebsockets[key]
            ?.firstWhereOrNull((ws) => ws.channel == channel)
            ?.ref ??
        '';
  }

  /// Listen to WebSocket messages
  void _listenToChannel(IOWebSocketChannel channel, String key, String ref) {
    debug("Listening to WebSocket with key [$key][$ref]");

    // Lắng nghe trên kênh WebSocket
    channel.stream.timeout(
      AppConfig.timeoutListenWebsocketDuration,
      onTimeout: (sink) {
        debug(
          "Timeout: No message received within ${AppConfig.timeoutListenWebsocketDuration.inSeconds} seconds.",
        );
        debugTrackingWSState();
        channel.sink.close();
      },
    ).listen(
      (message) {
        _notifyMessageListeners(key, message);
      },
      onError: (error) {
        _notifyErrorListeners(channel, key, error);
        final targetState = _activeWebsockets[key]?.firstWhereOrNull(
          (state) => state.channel == channel,
        );
        if (targetState != null) {
          targetState.status = WebSocketStatus.error;
        }
      },
      onDone: () {
        debug("WebSocket [$key] closed.");

        final targetState = _activeWebsockets[key]?.firstWhereOrNull(
          (state) => state.channel == channel,
        );
        if (targetState != null) {
          targetState.status = WebSocketStatus.closed;
          releaseWebSocket(key, targetState); // onDone:()
        }
      },
      cancelOnError: true,
    );
  }

  //// Register a new message listener for a specific WebSocket identified by `key`
  void addMessageListener(String key, Function(String) callback) {
    debug("WebSocket Add Message Listener for key [$key]");
    if (!_messageListenersByKey.containsKey(key)) {
      _messageListenersByKey[key] = [];
    }
    _messageListenersByKey[key]!.add(callback);
  }

  /// Remove a message listener for a specific WebSocket identified by `key`
  void removeMessageListener(String key, Function(String) callback) {
    debug("WebSocket Remove Message Listener for key [$key]");
    final listeners = _messageListenersByKey[key];
    if (listeners != null) {
      listeners.remove(callback);
      if (listeners.isEmpty) {
        _messageListenersByKey.remove(key);
      }
    }
  }

  /// Notify all registered listeners for a specific WebSocket `key` about a message
  void _notifyMessageListeners(String key, String message) {
    final listeners = _messageListenersByKey[key];
    if (listeners != null) {
      for (final callback in List.from(listeners)) {
        callback(message);
      }
    }
  }

  /// Send a message through the WebSocket to a specific WebSocketState
  void sendMessage(String key, String ref, String message) {
    final state =
        _activeWebsockets[key]?.where((state) => state.ref == ref).singleOrNull;
    if (state == null) {
      debug("No active WebSocket connections found for key [$key][$ref].");
      return;
    }

    // Gửi tin nhắn qua `channel` của state được xác định
    state.channel.sink.add(message);
    state.lastActive = DateTime.now();
    logWebSocketMessage(message);
    debugTrackingWSState();
  }

  void debugTrackingWSState() {
    debug("-------------------------------------------");
    final activeSocketCount =
        _activeWebsockets.values.expand((states) => states).length;

    final fullChunkCount = _activeWebsockets.values
        .expand((states) => states)
        .where((state) => state.status == WebSocketStatus.chunkFull)
        .length;

    final idleWSCount = _activeWebsockets.values
        .expand((states) => states)
        .where((state) => state.status == WebSocketStatus.idle)
        .length;

    final jsonActiveWS = formatActiveWebsockets();
    final jsonTask = formatTaskQueue();
    debug("Tracking add state to key SEND: $jsonActiveWS");

    debug(
      "ActiveSocketCount: $activeSocketCount. fullChunkCount: $fullChunkCount. IdleWSCount: $idleWSCount. _taskQueue: $jsonTask",
    );
  }

  void logWebSocketMessage(String message) {
    try {
      // Giải mã JSON thành Map
      final decodedMessage = json.decode(message) as Map<String, dynamic>;

      // Nếu có chunkData, xử lý rút gọn
      if (decodedMessage.containsKey('chunkData')) {
        // Lấy chunkData ban đầu
        final data = decodedMessage['chunkData'];
        // Rút gọn chunkData: lấy 10 phần tử đầu tiên và thêm hậu tố
        if (data is List) {
          decodedMessage['chunkData'] = '${data.take(10).toList()}... (Uint8)';
        }
      }

      if (decodedMessage.containsKey('fileData')) {
        final data = decodedMessage['fileData'];
        if (data is List) {
          decodedMessage['fileData'] = '${data.take(10).toList()}... (Uint8)';
        }
      }

      // In ra tin nhắn đã xử lý
      debug(
        "Message sent on WebSocket [${decodedMessage['type']}]: ${json.encode(decodedMessage)}",
      );
    } catch (e) {
      debug("Error parsing WebSocket message: $e");
    }
  }

  void handleGlobalDisconnection() {
    if (!hasAnyActiveConnection) {
      debug("No active WebSocket connections, taking appropriate actions...");
      // Xử lý nếu không còn kết nối nào
    }
  }

  /// Disconnect WebSocket connection
  void disconnect(String key) {
    if (!isConnected(key)) {
      debug("WebSocket [$key] is not connected, nothing to disconnect.");
      return;
    }

    final states = _activeWebsockets[key];
    if (states != null && states.isNotEmpty) {
      for (final state in states) {
        releaseWebSocket(key, state); // disconnect(String key)
      }
      debug("Message sent to all active WebSockets for key [$key].");
    } else {
      debug("No active WebSocket connections found for key [$key].");
    }

    debug("WebSocket [$key] disconnected.");
  }

  /// Check connection state for a specific key
  bool isConnected(String key) {
    final states = _activeWebsockets[key];
    return states != null &&
        states.any((state) => state.status == WebSocketStatus.active);
  }

  bool isCanNewConnection(String key) {
    final states = _activeWebsockets[key];
    final allConnectionFullChunk = states != null &&
        !states.any((state) => state.status != WebSocketStatus.chunkFull);
    final isMaxActiveSocket =
        _activeWebsockets.values.expand((states) => states).length >=
            maxActiveSockets;
    return allConnectionFullChunk || !isMaxActiveSocket;
  }

  bool hadIdleConnection(String key) {
    final states = _activeWebsockets[key];
    return states != null &&
        states.any((state) => state.status != WebSocketStatus.idle);
  }

  /// Check if any WebSocket connection is active
  bool get hasAnyActiveConnection {
    // Duyệt qua tất cả các danh sách `WebSocketState` trong Map và kiểm tra trạng thái active
    return _activeWebsockets.values.any(
      (states) => states.any((state) => state.status == WebSocketStatus.active),
    );
  }

  WebSocketState? getPreferredWebSocketState() {
    final allStates = _activeWebsockets.values.expand((states) => states);

    if (allStates.isEmpty) return null;

    final idleStates =
        allStates.where((state) => state.status == WebSocketStatus.idle);
    if (idleStates.isNotEmpty) {
      final idleState = idleStates
          .reduce((a, b) => a.lastActive.isAfter(b.lastActive) ? a : b);
      return idleState;
    }

    // Nếu không có trạng thái idle, tìm state tốt nhất dựa trên lastActive và currentChunks
    final backupState = allStates.reduce((a, b) {
      if (a.lastActive.isAfter(b.lastActive)) {
        return a;
      } else if (b.lastActive.isAfter(a.lastActive)) {
        return b;
      } else {
        return a.currentChunks < b.currentChunks ? a : b;
      }
    });

    return backupState;
  }

  String formatActiveWebsockets() {
    final formatted = _activeWebsockets.map((key, states) {
      return MapEntry(
        key,
        states.map((state) => state.toString()).toList(),
      );
    });
    return formatted.toString();
  }

  String formatTaskQueue() {
    return '[${_taskQueue.toList().map((task) => task.toString()).join(', ')}]';
  }
}
