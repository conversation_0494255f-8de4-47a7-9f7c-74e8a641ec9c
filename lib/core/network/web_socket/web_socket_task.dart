import 'dart:async';

import 'web_socket_state.dart';

class WebSocketTask implements Comparable<WebSocketTask> {
  final String key;
  final String ref;
  String? url;
  final int priority;
  final Map<String, dynamic>? headers;
  final Function? onComplete;
  final Function? onError;
  final Future<String> Function() getUrlCallback;

  final Completer<WebSocketState?> completer;

  WebSocketTask({
    required this.key,
    required this.ref,
    this.url,
    this.priority = 0,
    this.headers,
    this.onComplete,
    this.onError,
    required this.getUrlCallback,
    Completer<WebSocketState?>? completer,
  }) : completer = completer ?? Completer<WebSocketState?>();

  @override
  int compareTo(WebSocketTask other) {
    return priority.compareTo(other.priority);
  }

  @override
  String toString() {
    return '{key: $key, ref: $ref}';
  }
}
