import 'package:web_socket_channel/io.dart';

enum WebSocketStatus {
  idle,
  active,
  error,
  closed,
  chunkFull,
}

class WebSocketState {
  final IOWebSocketChannel channel;
  DateTime lastActive;
  WebSocketStatus status;
  int currentChunks;
  String ref;
  String url;

  WebSocketState({
    required this.channel,
    this.status = WebSocketStatus.idle,
    required this.lastActive,
    this.currentChunks = 0,
    required this.ref,
    required this.url,
  });

  @override
  String toString() {
    final timeDiffInSeconds = DateTime.now().difference(lastActive).inSeconds;
    return '{Ref: $ref, status: $status, chunks: $currentChunks, time: $timeDiffInSeconds(s)}';
  }
}
