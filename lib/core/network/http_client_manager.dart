import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:flutter/foundation.dart';

import '../data/models/api_response_classes/fs_base_response.dart';
import '../utils/exceptions/file_store_exception.dart';

class HttpClientManager {
  late final Dio dio;

  HttpClientManager({
    required String baseUrl,
    String? token,
  }) {
    dio = Dio(
      BaseOptions(
        baseUrl: baseUrl,
        connectTimeout: const Duration(seconds: 30),
        receiveTimeout: const Duration(seconds: 30),
        sendTimeout: const Duration(seconds: 30),
        headers: {
          'Content-Type': 'application/json',
          if (token != null) 'Authorization': 'Bearer $token',
        },
      ),
    );

    // Logging interceptor
    if (kDebugMode) {
      dio.interceptors.add(
        LogInterceptor(
          responseBody: true,
          requestBody: true,
        ),
      );
    }

    // Retry Interceptor
    dio.interceptors.add(
      RetryInterceptor(
        dio: dio,
        logPrint: print,
        retries: 3,
        retryDelays: const [
          Duration(seconds: 5), // Retry sau 5s
          Duration(seconds: 5),
          Duration(seconds: 5),
        ],
      ),
    );
  }

  /// Phương thức xử lý lỗi DioException
  void handleDioError(DioException e) {
    String msg = "";

    // Xử lý các loại lỗi khác nhau của Dio
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        msg = "Timeout occurred while sending or receiving";
        break;
      case DioExceptionType.unknown:
        msg = "No Internet Connection";
        break;
      case DioExceptionType.badCertificate:
        msg = "Internal Server Error";
        break;
      case DioExceptionType.connectionError:
        msg = "Connection Error";
        break;
      case DioExceptionType.cancel:
        msg = "Request was cancelled";
        break;
      default:
        msg = "Unknown Error occurred";
        break;
    }

    // Kiểm tra xem có phản hồi từ server hay không
    if (e.response != null) {
      try {
        final responseData = e.response!.data;
        final fsBaseResponse = FSBaseResponse<FSData>.fromJson(
          responseData is String
              ? jsonDecode(responseData)
              : responseData, // Nếu dữ liệu là chuỗi, parse JSON
          (json) => FSData.fromJson(json as Map<String, dynamic>),
        );

        final errorMessage = fsBaseResponse.error?.message;
        final code = fsBaseResponse.error?.code;
        msg = "Error: ${code ?? 'Unknown status code'} - $errorMessage";
      } catch (err) {
        // Nếu lỗi JSON, ghi log nhưng vẫn giữ thông báo cũ
        debugPrint("JSON parse error: $err");
      }
    }

    // Ném lỗi thành FileStoreException
    throw FileStoreException(msg);
  }

  Future<Response> get(
    String path, {
    Map<String, dynamic>? queryParameters,
    Map<String, dynamic>? headers,
  }) {
    return dio.get(
      path,
      queryParameters: queryParameters,
      options: Options(
        headers: headers,
      ),
    );
  }

  Future<Response> post(String path, {dynamic data}) {
    return dio.post(path, data: data);
  }

  Future<Response> put(String path, {dynamic data}) {
    return dio.put(path, data: data);
  }

  Future<Response> delete(String path, {dynamic data}) {
    return dio.delete(path, data: data);
  }
}
