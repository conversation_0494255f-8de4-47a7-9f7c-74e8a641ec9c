import 'package:connectivity_plus/connectivity_plus.dart';

class FSNetworkManager {
  final Connectivity _connectivity = Connectivity();
  ConnectivityResult _currentStatus = ConnectivityResult.none;

  FSNetworkManager() {
    _connectivity.onConnectivityChanged.listen((result) {
      _currentStatus = result.first;
    });
  }

  Stream<List<ConnectivityResult>> get onConnectivityChanged =>
      _connectivity.onConnectivityChanged;

  ConnectivityResult get currentStatus => _currentStatus;

  bool get isWifi => _currentStatus == ConnectivityResult.wifi;

  bool get isBluetooth => _currentStatus == ConnectivityResult.bluetooth;

  bool get isEthernet => _currentStatus == ConnectivityResult.ethernet;

  bool get isMobile => _currentStatus == ConnectivityResult.mobile;

  bool get isNone => _currentStatus == ConnectivityResult.none;

  bool get isVpn => _currentStatus == ConnectivityResult.vpn;

  bool get isOther => _currentStatus == ConnectivityResult.other;
}
