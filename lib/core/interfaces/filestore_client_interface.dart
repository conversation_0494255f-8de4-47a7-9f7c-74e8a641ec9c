import 'package:dio/dio.dart';

import '../utils/constants/enums.dart';
import '../implementations/upload_file.dart';

abstract class IFilestoreClient {
  Future<void> uploadFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  });

  Future<void> resumedFile(
    UpFile objFile, {
    void Function(UpFile objFile, String fileUrl)? onSuccess,
    void Function(UpFile objFile, ErrorCode code, String errorMessage)? onError,
    void Function(double progress)? onProgress,
    CancelToken? cancelToken,
    String? token,
    String? folderPath,
  });
}
