name: filestore_sdk
description: "A new FileStore package project."
version: 0.0.1

environment:
  sdk: ^3.5.4
  flutter: ">=1.17.0"

dependencies:
  flutter:
    sdk: flutter
  collection: ^1.17.2
  pool: ^1.5.0
  uuid: ^4.5.1


  dio: ^5.7.0
  web_socket_channel: ^3.0.1
  crypto: ^3.0.6
  path: ^1.9.0
  json_annotation: ^4.9.0
  logging: ^1.0.2
  dio_smart_retry: ^6.0.0
  connectivity_plus: ^6.1.0
  mime: ^2.0.0
  path_provider: ^2.1.5
  shared_preferences: ^2.3.3
  cross_file: ^0.3.4+2

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  json_serializable: ^6.8.0

  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # To add assets to your package, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/to/asset-from-package
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # To add custom fonts to your package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/to/font-from-package
